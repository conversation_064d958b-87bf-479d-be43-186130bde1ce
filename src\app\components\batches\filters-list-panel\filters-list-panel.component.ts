import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ButtonModule } from 'cax-design-system/button';
import { DividerModule } from 'cax-design-system/divider';

@Component({
    selector: 'app-filters-list-panel',
    standalone: true,
    imports: [DividerModule, ButtonModule, CommonModule],
    templateUrl: './filters-list-panel.component.html',
    styleUrl: './filters-list-panel.component.scss',
})
export class FiltersListPanelComponent {
    @Input() filtersApplied: any = {};
    public object = Object;

    getEntries(obj: Record<string, any>): [string, any][] {
        return Object.entries(obj);
    }

    getColumnName(name: string) {
        // Return the field name as header for now - can be enhanced with API data
        return name.charAt(0).toUpperCase() + name.slice(1).replace('_', ' ');
    }

    getFilterString(filter: any) {
        // Basic filter string representation - can be enhanced with API data
        const matchModeLabels: { [key: string]: string } = {
            'contains': 'Contains',
            'equals': 'Equals',
            'startsWith': 'Starts with',
            'endsWith': 'Ends with',
            'lt': 'Less than',
            'lte': 'Less than or equal',
            'gt': 'Greater than',
            'gte': 'Greater than or equal',
            'dateIs': 'Date is',
            'dateBefore': 'Date before',
            'dateAfter': 'Date after'
        };

        const label = matchModeLabels[filter.matchMode] || filter.matchMode;
        return label + (filter.value ? ` ${filter.value}` : '');
    }

    clearFilter() {
        console.log('event');
    }

    removeAllFilters() {
        console.log('remove');
    }
}
