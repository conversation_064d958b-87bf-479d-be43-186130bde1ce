<div class="tags-container">
    <div class="action-container">
        <div>
            <cax-inputtext
                [(ngModel)]="searchQuery"
                (input)="onSearch()"
                [placeholder]="'Search Tags...'"
                [leftIcon]="true"
                [clearIcon]="true"
                [style]="{ width: '560px' }"
                [leftIconClass]="'cax cax-magnifier'">
            </cax-inputtext>
        </div>
        <div class="button-container">
            <cax-button
                label="Create New Tag"
                [leftIcon]="'cax cax-add-bold'"
                (click)="openCreateTagSidebar()">
            </cax-button>
        </div>
    </div>
    <div class="table-container">
        <cax-table
            [columns]="columns"
            [value]="filteredTags"
            [(selection)]="selectedTags"
            styleClass="cax-datatable-gridlines">
            <ng-template caxTemplate="header" let-columns>
                <tr>
                    <th>
                        <cax-tableHeaderCheckbox />
                    </th>
                    <th *ngFor="let col of columns">
                        {{ col.header }}
                    </th>
                </tr>
            </ng-template>
            <ng-template
                caxTemplate="body"
                let-rowData
                let-columns="columns"
                let-index="rowIndex">
                <tr>
                    <td>
                        <cax-tableCheckbox [value]="rowData" [index]="index" />
                    </td>
                    <td *ngFor="let col of columns">
                        <ng-container [ngSwitch]="col.field">
                            <!-- Tag column -->
                            <ng-container *ngSwitchCase="'name'">
                                <div class="tag-container">
                                    <cax-chip
                                        [label]="rowData.name"
                                        [severity]="rowData.severity"
                                        [style]="rowData.customStyle"
                                        [size]="'md'"
                                        (click)="onTagClick(rowData)">
                                    </cax-chip>
                                    <i
                                        *ngIf="rowData.history?.length"
                                        class="cax cax-layers-minimalistic"
                                        (click)="
                                            showTagHistory($event, rowData)
                                        "></i>
                                </div>
                            </ng-container>
                            <!-- Status column -->
                            <ng-container *ngSwitchCase="'status'">
                                <div class="toggle-container">
                                    <span class="status-text">{{
                                        rowData.isActive ? 'Active' : 'Inactive'
                                    }}</span>
                                    <cax-toggleswitch
                                        [size]="'md'"
                                        [checked]="rowData.isActive"
                                        [disabled]="false"
                                        (click)="
                                            onStatusToggle($event, rowData)
                                        ">
                                    </cax-toggleswitch>
                                </div>
                            </ng-container>

                            <!-- Default for other columns -->
                            <ng-container *ngSwitchDefault>
                                {{ rowData[col.field] }}
                            </ng-container>
                        </ng-container>
                    </td>
                </tr>
            </ng-template>
        </cax-table>
    </div>
</div>

<!-- Tag History Overlay Panel -->
<cax-overlayPanel
    #tagHistoryPanel
    [style]="{ overflow: 'auto' }"
    [showCloseIcon]="false">
    <cax-timeline [value]="tagHistoryList">
        <ng-template caxTemplate="content" let-event>
            {{ event }}
        </ng-template>
    </cax-timeline>
</cax-overlayPanel>

<!-- Confirmation Dialog -->
<cax-confirmDialog></cax-confirmDialog>

<!-- Tag Sidebar -->
<app-create-new-tag-sidebar
    [(visible)]="tagSidebarVisible"
    [tag]="selectedTag"
    (close)="onSidebarClose()"
    (createTag)="onCreateNewTag($event)"
    (updateTag)="onTagUpdated($event)">
</app-create-new-tag-sidebar>
