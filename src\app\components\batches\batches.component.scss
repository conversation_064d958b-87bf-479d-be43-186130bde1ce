.action-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    gap: 16px;

    .search-container {
        flex: 1;
        max-width: 560px;
    }

    .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
}

.table-container {
    padding: 0 24px;
}

.paginator-container {
    padding: 16px 24px;
}

.data-container {
    .batch-name {
        color: var(--primary-color);
        font-weight: 500;
        span {
            cursor: pointer;
        }
        i {

            cursor: pointer;
        }
    }
}

.batch-status-panel {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.upload-button-container {
    display: flex;
    align-items: center;
    gap: 16px;

    cax-button {
        flex: 1;
    }
}

.autocomplete-group-item{
  background: none !important;
}

.autocomplete-item{
        display: flex;
        align-items: center;
        justify-content: space-between;

    .item-title{
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 4px;

        span{
            font-size: 14px;
            font-weight: 400;
            color: #17181A;
        }
    }

    .batchId{
        background-color: #E7EDFE;
        padding: 3.5px 5.6px;
        border-radius: 6px;
        color: #4E5155;
        font-size: 12px;
        font-weight: 500;
    }


}

::ng-deep .cax-autocomplete-panel .cax-autocomplete-items .cax-autocomplete-item-group{
       background: none;
}

::ng-deep .cax-upload-menu{
    width: auto;
}

::ng-deep .cax-datepicker .cax-datepicker-header{
    border: none;
}

.eta-cell {
    position: relative;
  }

  .calendar-popup-container {
    position: absolute;
    z-index: 1000;
    background-color: white;
    left: 0;
    top: 100%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }
.comment-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.comments-container {
    display: flex;
    cursor: pointer;
    height: 100%;
}

.references-container {
    display: flex;
    flex-direction: column;
    gap: 4px;

    a {
        color: var(--primary-500);
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

}

.accepted-rows-container {
    font-size: 14px;
    font-weight: 500;
    .fw-bold {
        color: var(--neutral-900);
    }
    .fw-light {
        color: var(--neutral-600);
    }
}

.user-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-email {
        font-size: 12px;
        color: var(--neutral-600);
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.date-container {
    font-size: 12px;
    color: var(--neutral-700);
}

.batch-id-container {
    display: flex;
    align-items: center;
    gap: 4px;

    .batch-id-link {
        color: var(--primary-500);
        cursor: pointer;
        font-weight: 500;

        &:hover {
            text-decoration: underline;
            color: var(--primary-600);
        }
    }
}

.name-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .batch-name {
        color: var(--neutral-700);
        font-weight: normal;
    }

    .download-icon {
        color: var(--primary-500);
        cursor: pointer;
        margin-left: auto;
        font-size: 14px;

        &:hover {
            color: var(--primary-600);
        }
    }
}

.description-container {
    .description-text {
        font-size: 12px;
        color: var(--neutral-700);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

.references-container {
    .reference-link {
        color: var(--primary-500);
        text-decoration: none;
        font-size: 12px;

        &:hover {
            text-decoration: underline;
        }
    }

    .no-reference {
        color: var(--neutral-500);
        font-size: 12px;
        font-style: italic;
    }
}

.sc-batch-id-container {
    font-size: 12px;
    color: var(--neutral-700);
}

.actions-container {
    display: flex;
    justify-content: center;
    align-items: center;

}

.comments-container {
    cursor: pointer;

    .comment-text {
        font-size: 12px;
        color: var(--neutral-600);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }
}

.comment-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;

    i {
        font-size: 14px;
    }

    .comment-text {
        font-size: 12px;
        color: var(--neutral-600);
    }
}

.no-tags {
    font-size: 12px;
    color: var(--neutral-500);
    font-style: italic;
}


.selection-sidebar-content {
    display: flex;
    flex-direction: column;

    .fields-container {
        display: flex;
        align-items: flex-end;
        gap: 16px;
        width: 100%;
    }

    .input-container {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .button-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .divider {
        height: 42px;
        width: 1px;
        background-color: var(--neutral-100);
    }

}
.tag-container{
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 4px;
        padding-top: 4px;
       padding-bottom: 4px;
}
.chip-tag{
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding-top: 12px;
        padding-bottom: 12px;

}
.create-tag-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 12px;
}

.no-tags-bg{
    text-align: center;
    background-color: var(--neutral-25);
}
.empty-tag{
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.custom-icon{
    font-size: 40px;
    color: var(--neutral-500);
}

.no-tags-heading {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 0.5rem;
    color: var(--neutral-900);
}

.no-tags-desc {
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 1rem;
    color: var(--neutral-700);
}
