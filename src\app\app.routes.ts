import { Routes } from '@angular/router';
import { LoadingComponent } from './components/loading/loading.component';
import { AppAuthGuard } from './_guards/auth.guard';
import { AuthGuard } from '@auth0/auth0-angular';
import { BatchesComponent } from './components/batches/batches.component';

export const routes: Routes = [
  {
    path: 'home',
    component: BatchesComponent,
    data: { title: 'Batches' },
    canActivate: [AppAuthGuard],
  },
  {
    path: 'loading',
    component: LoadingComponent,
    data: { title: 'Loading' },
    canActivate: [AuthGuard],
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'home',
  },
];
