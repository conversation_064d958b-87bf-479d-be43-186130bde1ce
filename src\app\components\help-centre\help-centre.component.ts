import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'cax-design-system/button';
import { EditorModule } from 'cax-design-system/editor';
import { InputTextModule } from 'cax-design-system/inputtext';
import { SidebarModule } from 'cax-design-system/sidebar';
import { UploadModule } from 'cax-design-system/upload';

export interface Ticket {
  title: string;
  description: string;
  files: File[]; 
}

@Component({
    selector: 'app-help-centre',
    standalone: true,
    imports: [ButtonModule, SidebarModule, EditorModule , InputTextModule , UploadModule ,FormsModule],
    templateUrl: './help-centre.component.html',
    styleUrl: './help-centre.component.scss',
})
export class HelpCentreComponent {
    ticketSidebarVisible: boolean = false;
    tickets: Ticket[] = [];
    
    newTicket = {
        title: '',
        description: '',
        files: [] as File[]
     };


    createTicket() {
        this.ticketSidebarVisible = true;
    }


   // Handle text input
    handleTextChange(event: any) {
    this.newTicket.description = event.htmlValue;
    }

    // Handle file upload
    handleFileUpload(event: any) {
    this.newTicket.files = event.files; 
    }

    creatTicket() {
    if (this.newTicket.title.trim()) {
        this.tickets.push({ ...this.newTicket });
        console.log('Tickets:', this.tickets);
        // Reset form
        this.newTicket = { title: '', description: '', files: [] };
        this.ticketSidebarVisible = false;
    } else {
        console.warn('Title is required!');
       }
    }

    cancleTicket(){
        this.newTicket = { title: '', description: '', files: [] };
        this.ticketSidebarVisible = false;
    }
}
