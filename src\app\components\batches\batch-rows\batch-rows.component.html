<div class="action-container">
    <div class="search-container">
        <cax-autoComplete
            [style]="{ width: '100%' }"
            [placeholder]="'Search...'" />
    </div>
    <div class="button-container">
        <cax-button
            severity="secondary"
            icon="cax cax-tuning"
            caxBadge
            value="5"
            badgeSize="xs"
            badgeSeverity="danger"
            (click)="filtersPanel.toggle($event)"></cax-button>
        <cax-button
            severity="secondary"
            (click)="openBatchInfo()"
            icon="cax cax-info-circle"></cax-button>
        <cax-button
            severity="secondary"
            (click)="tableSizePanel.toggle($event)"
            icon="cax cax-text-icon-bold"></cax-button>
        <cax-button
            severity="secondary"
            (click)="openColumnsList()"
            icon="cax cax-layers"></cax-button>
        <cax-button
            leftIcon="cax cax-download"
            outlined="true"
            label="Download"></cax-button>
    </div>
</div>
<div class="table-container">
    <cax-table
        [columns]="sortedSelectedColumns"
        styleClass="cax-datatable-gridlines"
        [value]="skuList"
        [scrollable]="true"
        [fontSize]="skuFontSize"
         (selectionChange)="onTableSelectionChange($event)"
        [rowSize]="skuRowSize">
        <ng-template caxTemplate="header" let-columns>
            <tr>
                <th
                    caxColumnWidth
                    [minWidth]="'60px'"
                    [maxWidth]="'60px'"
                    caxFrozenColumn>
                    <cax-tableHeaderCheckbox />
                </th>
                <th
                    caxColumnWidth
                    [minWidth]="col.minWidth"
                    [maxWidth]="col.maxWidth"
                    caxFrozenColumn
                    [frozen]="col.fixed"
                    *ngFor="let col of columns">
                    {{ col.header }}
                </th>
            </tr>
        </ng-template>
        <ng-template
            caxTemplate="body"
            let-rowData
            let-index="rowIndex"
            let-columns="columns">
            <tr>
                <td caxFrozenColumn>
                    <cax-tableCheckbox [index]="index" [value]="rowData" />
                </td>
                <td
                    caxFrozenColumn
                    [frozen]="col.fixed"
                    *ngFor="let col of columns">
                    <div
                        class="w-100 h-100 data-container"
                        [ngSwitch]="col.field">
                        <!-- case for the rowid -->
                          <div
                                    *ngSwitchCase="'row_id'"
                                    class="d-flex align-center spc-btwn batch-name">
                                    <span (click)="openBatchDetails(rowData)">{{
                                        rowData[col.field]
                                    }}</span>
                                    <i
                                        *ngIf="rowData.audit_log?.length"
                                        class="cax cax-layers-minimalistic"
                                        (click)="
                                            openBatchLogPanel($event, rowData)
                                        "></i>
                            </div>
                         <!-- case for batch status -->
                                <div *ngSwitchCase="'status'">
                                    <cax-chip
                                        (click)="
                                            openBatchStatusPanel(
                                                $event,
                                                rowData[col.field],
                                                index
                                            )
                                        "
                                        [label]="
                                            getBatchStatus(rowData[col.field])
                                                .name
                                        "
                                        [severity]="
                                            getBatchStatus(rowData[col.field])
                                                .severity
                                        "
                                        [icon]="
                                            getBatchStatus(rowData[col.field])
                                                .icon
                                        "
                                        [size]="size"></cax-chip>
                                </div>
                        <!-- case for comments -->
                                <div
                                    *ngSwitchCase="'comments'"
                                    class="comments-container"
                                    (click)="openComments(rowData)">
                                    <!-- Custom avatar for the comment sender -->
                                    <cax-avatar
                                        [label]="rowData.comments?.length && rowData.comments[0]?.sender 
                                                ? rowData.comments[0].sender.substring(0, 2).toUpperCase() 
                                                : 'NA'"
                                        [avatarSize]="avatarSize"
                                        [shape]="'circle'"
                                        [style]="{
                                            marginRight: '8px',
                                        }"></cax-avatar>

                                    <!-- Comment text -->
                                    <div class="comment-text">
                                        {{
                                            rowData.comments?.length
                                                ? (rowData.comments[
                                                      rowData.comments.length -
                                                          1
                                                  ].text | slice: 0 : 30) +
                                                  (rowData.comments[
                                                      rowData.comments.length -
                                                          1
                                                  ].text?.length > 30
                                                      ? '...'
                                                      : '')
                                                : 'No comments'
                                        }}
                                    </div>
                                </div>
                        <!-- default case -->
                        <div *ngSwitchDefault>
                            {{ rowData[col.field] }}
                        </div>
                    </div>
                </td>
            </tr>
        </ng-template>
    </cax-table>
</div>
<div class="paginator-container">
    <cax-paginator
        [pageLinkSize]="5"
        [currentPageReportTemplate]="
            'Showing {currentPage} to {totalPages} of {totalRecords} enteries'
        "
        [showCurrentPageReport]="true"
        [showFirstLastIcon]="true"
        [totalRecords]="3"
        [rows]="15"
        [rightAligned]="true"
        [rowsPerPageOptions]="[15, 30, 50, 100]"
        [showJumpToPageInput]="true"
        [showPageLinks]="true"></cax-paginator>
</div>

<!-- table configuration overlay panel -->

<cax-overlayPanel
    [style]="{ width: '478px' }"
    #tableSizePanel
    [dismissable]="true">
    <cax-tableconfiguration
        (fontSizeChange)="onFontSizeChange($event)"
        (rowHeightChange)="onRowHeightChange($event)"></cax-tableconfiguration>
</cax-overlayPanel>

<!-- column list sidebar -->

<cax-sidebar
    [headerText]="'Columns (' + skuColumnHeaders.length + ')'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '390px' }"
    [(visible)]="columnSidebarVisible">
    <app-column-list-sidebar
        [columnListData]="skuColumnHeaders"
        (emitFilteredColumns)="
            filterColumnsList($event)
        "></app-column-list-sidebar>
</cax-sidebar>

<!-- filters list overlay panel -->

<cax-overlayPanel
    [style]="{ width: '332px' }"
    #filtersPanel
    [dismissable]="true">
    <app-filters-list-panel></app-filters-list-panel>
</cax-overlayPanel>

<!-- batch info sidebar -->

<cax-sidebar
    [headerText]="'Details'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '480px' }"
    [(visible)]="batchInfoSidebarVisible">
    <div class="batch-details">
        <div class="info-list">
            <span class="info-column-name">Batch Name</span>
            <span class="info-column-text">Turck New SKUs AD24AR001</span>
        </div>
        <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">Batch ID</span>
            <span class="info-column-text">Turck New SKUs AD24AR001</span>
        </div>
        <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">Status</span>
            <span class="info-column-text">
                <cax-chip
                    severity="warning"
                    label="Completed"
                    [size]="'sm'"></cax-chip>
            </span>
        </div>
        <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">SKUs</span>
            <span class="info-column-text">Turck New SKUs AD24AR001</span>
        </div>
        <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">Tags</span>
            <span class="info-column-text">
                <cax-chip
                    removable="true"
                    [size]="'sm'"
                    label="Priority_M"></cax-chip>
                <cax-chip
                    severity="warning"
                    [size]="'sm'"
                    label="direct_sync"></cax-chip>
            </span>
        </div>
        <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">Uploaded By</span>
            <span class="info-column-text">
               <p>USR 1001</p>
            </span>
        </div>
         <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
        <div class="info-list">
            <span class="info-column-name">Uploaded on</span>
            <span class="info-column-text">
               <p>21/12/2025</p>
            </span>
        </div>
    </div>

</cax-sidebar>
<cax-sidebar
    [headerStyle]="{
        fontSize: '16px',
    }"
    [headerText]="currentBatchHeader"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '480px' }"
    [(visible)]="openComment"
>
<cax-comments
    [comments]="currentBatchComments"
    [sidebarHeader]="currentBatchHeader"
    [mentionSuggestions]="mentionSuggestions"
    [hashtagSuggestions]="hashtagSuggestions"
    [isAdmin]="true"
    (commentAdded)="onCommentAdded($event)"
    [(visible)]="commentsSidebarVisible">
</cax-comments>
</cax-sidebar>

<!-- for the filter sidebar -->
<cax-sidebar
    [style]="{
        left: '50%',
        width: 'max-content',
        transform: 'translate(-50%, 0)',
        height: 'auto',
    }"
    [headerStyle]="{
        background: 'var(--neutral-800)',
        color: 'var(--white-100)',
    }"
    [position]="'bottom'"
    [headerText]="selectedBatches.length + ' Selected'"
    [modal]="false"
    [dismissible]="false"
    [(visible)]="selectionSidebarVisible">
    <div class="selection-sidebar-content">
        <div class="fields-container">
            <!-- Status -->
            <div class="input-container">
                <cax-dropdown
                    [labelText]="'Status'"
                    [appendTo] = "'body'"
                    [options]="statusOptions"
                    [(ngModel)]="selectedBatchStatus"
                    [placeholder]="'Change Status'"
                    [style]="{ width: '180px' }"></cax-dropdown>
            </div>

            <!-- Assignee -->
            <div class="input-container">
               <cax-inputtext
                    [label]="'Category'"
                    [placeholder]="'Enter your comments here...'"
                    [(ngModel)]="selectedBatchesCategory"
                    [style]="{ width: '300px' }"></cax-inputtext>
            </div>

            <!-- Comments -->
            <div class="input-container">
                <cax-inputtext
                    [label]="'Add Comments'"
                    [placeholder]="'Enter your comments here...'"
                    [(ngModel)]="selectedBatchesComment"
                    [style]="{ width: '300px' }"></cax-inputtext>
            </div>

            <!-- Buttons -->
            <div class="button-wrapper">
                <cax-button
                    label="Save"
                    [disabled]="!isSaveEnabled()"
                    (click)="saveAllChanges()"
                    [size]="'medium'">
            </cax-button>
            </div>
        </div>
    </div>
</cax-sidebar>

<!-- common dailog box -->
<cax-confirmDialog></cax-confirmDialog>   

<!-- batch log overlay panel -->

<cax-overlayPanel
    [style]="{ width: 'inherit' }"
    #batchLogPanel
    [dismissable]="true">
    <cax-timeline [value]="batchLogList">
        <ng-template caxTemplate="content" let-event>
            {{ event }}
        </ng-template>
    </cax-timeline>
</cax-overlayPanel>

<!-- for the attribute -->
<cax-dialog
        [(visible)]="batchDetailsDialogVisible"
        [modal]="true"
        [style]="{ width: '70vw', height: '80%' }"
        [closable]="false"
>
    <!-- Header -->
    <ng-template caxTemplate="header">
            <div class="custom-dialog-header d-flex align-center justify-between w-100">
            <div class="custom-dialog-left d-flex align-center justify-between">
                 <i
                    class="cax cax-round-alt-arrow-left cax-icons"
                    [ngClass]="{ 'disabled-icon': isPreviousDisabled() }"
                    [style.cursor]="isPreviousDisabled() ? 'not-allowed' : 'pointer'"
                    (click)="!isPreviousDisabled() && goToPreviousBatch()"
                    ></i>
                <div class="custom-dialog-name">
                <span class="batch-id">{{ selectedBatch?.row_id }} ({{attributeList.length || null }})</span>
                <cax-chip
                    *ngIf="selectedBatch?.status"
                    [label]="getBatchStatus(selectedBatch.status).name"
                    [severity]="getBatchStatus(selectedBatch.status).severity"
                    [icon]="getBatchStatus(selectedBatch.status).icon"
                    [size]="size"
                ></cax-chip>
                </div>
              <i
                class="cax cax-round-alt-arrow-right cax-icons"
                [ngClass]="{ 'disabled-icon': isNextDisabled() }"
                [style.cursor]="isNextDisabled() ? 'not-allowed' : 'pointer'"
                (click)="!isNextDisabled() && goToNextBatch()"
                ></i>
            </div>

                <div class="d-flex align-center icon-right">
                    <i class="cax cax-chat-line cax-icons"
                    style="cursor: pointer"
                    [ngStyle]="{ color: commentsVisible ? '#007bff' : 'inherit' }"
                    (click)="toggleComments()"
                    ></i>
                    <i
                    class="cax cax-clock-circle cax-icons"
                    style="cursor: pointer"
                    [ngStyle]="{ color: logVisible ? '#007bff' : 'inherit' }"
                    (click)="toggleLog()"
                    ></i>
                    <i
                    class="cax cax-close-circle cax-icons"
                    (click)="batchDetailsDialogVisible = false"
                    style="cursor: pointer"
                    ></i>
                </div>
            </div>
    </ng-template>

    <!-- Main Content Area with Sidebar -->
    <div class="dialog-content-flex">
        <!-- Left Content (Toolbar + Form) -->
            <div class="main-content" [ngClass]="{ 'with-log': logVisible || chatVisible || commentsVisible }">
            <!-- Top Toolbar -->
            <div class="top-level">
                <div class="search-attribute">
                <cax-autoComplete placeholder="Search..." class="search-attribute"></cax-autoComplete>
                </div>
                <div class="button">
                <cax-dropdown
                    [placeholder]="'Move To'"
                    [options]="chipOptions"
                    [(ngModel)]="selectedOption"
                    optionLabel="label"
                    [showClear]="false"
                    [style]="{ width: '200px' }"
                    >
                    <!-- Template for dropdown items -->
                    <ng-template let-option caxTemplate="item">
                        <cax-chip
                        [label]="option.label"
                        [severity]="option.severity"
                        [icon]="option.icon"
                        [size]="size"
                        ></cax-chip> 
                    </ng-template>

                    <!-- Template for selected item (when selected) -->
                    <ng-template let-selected caxTemplate="selectedItem">
                        <cax-chip
                        *ngIf="selected"
                        [label]="selected.label"
                        [severity]="selected.severity"
                        [size]="size"
                        ></cax-chip>
                        <span *ngIf="!selected" class="cax-dropdown-label">Select</span>
                    </ng-template>
                </cax-dropdown>
                <cax-button label="Save changes" [severity]="'primary'" [size]="'medium'" (click)="showSaveConfirmation($event)">
                </cax-button>
                </div>
            </div>

                <!-- Form Content -->
                  <div class="form-wrapper">
                        <div class="form-grid">
                            <div class="form-field" *ngFor="let field of attributeList">
                                <label>{{ field.label }}</label>
                                <div class="input-container" 
                                    [class.has-value]="field.value"
                                    [class.is-readonly]="field.value">
                                    <input
                                        caxInputText
                                        [value]="field.value"
                                        [placeholder]="field.placeholder"
                                        [readonly]="field.value"
                                        [ngStyle]="{
                                            backgroundColor: field.value ? '#F6F7F8' : '#ffffff',
                                            cursor: field.value ? 'not-allowed' : 'text'
                                        }"
                                    /> 
                                    <!-- Upload icon for document/image fields -->
                                    <i class="upload-icon cax cax-square-top-up" 
                                    *ngIf="isDocumentOrImageField(field.label)"
                                    title="Upload {{ field.label }}"
                                    (click)="openChatForUpload(field)"
                                    style="cursor: pointer; color: #5946b9; margin-left: 8px;"></i>
                                </div>
                            </div>
                        </div>
                  </div>
            </div>

        <!-- Right Sidebar for Comments -->
        <div *ngIf="commentsVisible" class="log-sidebar">
            <div class="main-heading">
                <p class="document-heading">Comments</p>
                <i class="cax cax-close cax-icons" (click)="toggleComments()"></i>
            </div>
            <div class="line"></div>
            
           <div *ngIf="commentsRendered" class="comment-contaiener">
                <cax-comments
                    [comments]="commentsData"
                    [mentionSuggestions]="mentionSuggestionsData"
                    [hashtagSuggestions]="hashtagSuggestionsData"
                    (visibleChange)="onCommentsVisibleChange($event)"
                    (commentAdded)="onCommentAdded($event)"
                    #caxComments>
                </cax-comments>
            </div>
                            
        </div>

        <!-- Right Sidebar for the log  -->
            <div *ngIf="logVisible" class="log-sidebar">
                 <p class="heading">Change Log</p>
                 <div class="line"></div>
                <cax-timeline [value]="mockLogs" class="timeline-data">
                    <ng-template let-log caxTemplate="content">
                    <div class="timeline-entry">
                        <p class="user-date">
                        <strong>{{ log.user }}</strong> • {{ log.date }}
                        </p>
                        <ul class="change-list">
                        <li *ngFor="let change of log.changes">
                            <span [innerHTML]="change"></span>
                        </li>
                        </ul>
                    </div>
                    </ng-template>
                </cax-timeline>
            </div>

        <!-- right side bar for image and the doc -->
            <div *ngIf="chatVisible" class="log-sidebar">
                <div class="main-heading">
                    <p class="document-heading">{{ selectedFieldForUpload?.label || 'Documents are here' }}</p>
                    <i class="cax cax-close cax-icons" (click)="toggleChat()"></i>
                </div>
                <div class="line"></div>

                <!--  Image preview if value is image URL -->
                <div *ngIf="isImageUrl(selectedFieldForUpload?.value); else showFileName" class="image-preview-container">
                    <div class="image-container">
                    <img [src]="selectedFieldForUpload.value" alt="Image Preview" class="image-preview" />
                    </div>
                </div>

                <!--  File name if not image -->
                <ng-template #showFileName>
                         <div *ngIf="selectedFieldForUpload?.value" class="document-list">
                              <div class="document-name">{{ selectedFieldForUpload.value }}</div>
                         </div>
                </ng-template>

                <!-- Action Buttons -->
                <div class="chat-actions">
                    <cax-button 
                    label="Replace" 
                    [severity]="'secondary'" 
                    [leftIcon]="'cax cax-download-bold'" 
                    [size]="'medium'"
                    [style]="{ width: '100%' }"
                    [disabled]="!selectedFieldForUpload?.value"
                    (click)="replaceDocument()">
                    </cax-button>
                    <cax-button 
                    label="Download" 
                    [severity]="'primary'" 
                    [style]="{ width: '100%' }"
                    [size]="'medium'"
                    [leftIcon]="'cax cax-download-bold'"
                    [disabled]="!selectedFieldForUpload?.value"
                    (click)="downloadDocument()">
                    </cax-button>
                </div>
            </div>

    </div>

</cax-dialog>