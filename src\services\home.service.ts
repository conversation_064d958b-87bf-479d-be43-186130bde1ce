import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient, HttpParams } from '@angular/common/http';
import { Globals } from '../app/_globals/endpoints.global';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class HomeService {
  private httpOptions: HttpHeaders;

  public messageSource = new BehaviorSubject(null);

  // private endpoints: any = ENDPOINTS;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });

  }

  /**
   *
   * @param response
   * get batch list
   */
  getBatchList = (
    page: string,
    size: string,
    status: string,
    search: string,
    start_date: string,
    end_date: string,
    subscription_id: string,
    tags: string[]
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('status', status)
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    tags.forEach((item: string) => {
      options.params = options.params.append('label[]', item);
    });
    const BatchListEndpoint = this.globals.urlJoin('home', 'batchList');
    let url = BatchListEndpoint;
    if (subscription_id) {
      url += subscription_id + '/batches/list';
    } else {
      url += 'batches/list';
    }
    return this.http
      .get(url, options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * download input file
   * @param subscription_id
   * @param batch_id
   * @returns
   */
  downloadInputFile = (subscription_id: string, batch_id: string): Observable<any> => {
    const downloadInputFileEndpoint = this.globals.urlJoinWithTwoParam('home', 'inputFileDownload', subscription_id, batch_id)
    return this.http
      .get(downloadInputFileEndpoint)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  }

  /**
   * Get single batch list
   * @param subscription_id
   * @param batch_id
   * @returns
   */
  getSingleBatchList = (subscription_id: string, batch_id: string, bucket: string): Observable<any> => {
    const SingleBatchListEndpoint = this.globals.urlJoin(
      'home',
      'singleBatchList'
    );
    return this.http
      .post(
        SingleBatchListEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/output_file_status',
          bucket == 'ALL' ? {} : { bucket: bucket }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Get stats
   */
  getStats = (subscription_id: string): Observable<any> => {
    const StatsEndpoint = this.globals.urlJoin('home', 'stats');
    return this.http.get(StatsEndpoint + subscription_id + '/stats').pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * Get Labels
   */
  getLabelList = (subscription_id: string): Observable<any> => {
    const LabelListEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .get(LabelListEndpoint + subscription_id + '/label_list')
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * add label
   */
  addLabel = (
    subscription_id: string,
    batch_id: string,
    addlabel_id: string,
    removelabel_id: string
  ): Observable<any> => {
    const LabelEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .patch(
        LabelEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/add_remove_labels',
        {
          add_label: addlabel_id,
          remove_label: removelabel_id,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * status update
   */
  statusUpdate = (subscription_id: string, batch_id: string, status: string): Observable<any> => {
    const statusUpdateEndpoint = this.globals.urlJoin('home', 'statusUpdate');
    return this.http
      .patch(
        statusUpdateEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/status_update',
        {
          status: status,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Delete batch
   */
  deleteBatchList = (subscription_id: string, batch_id: string): Observable<any> => {
    const deleteBatchListEndpoint = this.globals.urlJoin('home', 'deleteBatch');
    return this.http
      .delete(
        deleteBatchListEndpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/delete'
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  // approve/cancel batch
  approveORcancel = (subscription_id: string, batch_id: string, statusStr: string): Observable<any> => {
    const approveORcancelEndpoint =
      '/api/subscriptions/' +
      subscription_id +
      '/batches/' +
      batch_id +
      '/status_update';
    return this.http
      .patch(approveORcancelEndpoint, {
        status: statusStr,
      })
      .pipe(
        map((response) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * add label
   */
  createLabel = (subscription_id: string, name: string, title: string, tagBgColor: string, tagTextColor: string): Observable<any> => {
    const createLabelEndpoint = this.globals.urlJoin('home', 'labelList');
    return this.http
      .patch(createLabelEndpoint + subscription_id + '/create_label', {
        name: name,
        bg_colour_code: tagBgColor,
        text_colour_code: tagTextColor
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Get status count
   */
  getStatusCount = (
    subscription_id: string,
    search: string,
    start_date: string,
    end_date: string,
    tags: string[]
  ): Observable<any> => {
    console.log("ctrl here")
    const countEndpoint = this.globals.urlJoin('home', 'count');
    const options = {
      params: new HttpParams()
        .set('q', search)
        .set('start_date', start_date)
        .set('end_date', end_date)
    };
    tags.forEach((item: string) => {
      options.params = options.params.append('label[]', item);
    });
    return this.http
      .get(countEndpoint + subscription_id + '/batches/status_count', options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(() => error))
      );
  };

  /**
   * generate output file and insufficient data
   * @param subscription_id
   * @param batch_id
   * @returns
   */
  generateBucketFile = (subscription_id: string, batch_id: string, bucket: string): Observable<any> => {
    const fileGenerateEndPoint = this.globals.urlJoin('home', 'generateOutput');
    console.log(bucket);
    return this.http
      .post(
        fileGenerateEndPoint +
        subscription_id +
        '/batches/' +
        batch_id +
        '/generate_output',
        bucket == 'ALL' ? {} : { bucket: bucket }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  downloadFile = (subscription_id: string, batch_id: string, bucket: string): Observable<any> => {
    const outputFileEndpoint = this.globals.urlJoin(
      'home',
      'downloadOutputFile'
    );
    return this.http
      .get(
        outputFileEndpoint +
          subscription_id +
          '/batches/' +
          batch_id + '/'+bucket +
          '/download_output'
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get batch/sku log
   * @param subscription_id
   * @param category
   * @param category_id
   * @returns
   */
  getLog = (subscription_id: string, category: string, category_id: string): Observable<any> => {
    const getLogDetailsEndpoint = this.globals.urlJoinWithParam(
      'home',
      'batchLog',
      subscription_id
    );
    const options = {
      params: new HttpParams()
        .set('category', category)
        .set('category_id', category_id),
    };
    return this.http.get(getLogDetailsEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get template id for selected file format
   * @param subscription_id
   * @param type
   * @returns
   */
  getTemplateId = (subscription_id: string, type: string): Observable<any> => {
    const getTemplateIdEndpoint = this.globals.urlJoinWithParam(
      'home',
      'getTemplateId',
      subscription_id
    );
    const options = {
      params: new HttpParams().set('type', type),
    };
    return this.http.get(getTemplateIdEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * update ETA
   * @param subscription_id
   * @param batch_id
   * @param date
   * @returns
   */
  modifyETA = (subscription_id: string, batch_id: string, date: string): Observable<any> => {
    const updateETAEndPoint = this.globals.urlJoin('home', 'updateETA');
    return this.http
      .post(
        updateETAEndPoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/add_eta',
        {
          eta: date,
        }
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * Export all batches
   * @param subscription_id
   * @returns
   */
  exportBatches = (subscription_id: string): Observable<any> => {
    const exportEndpoint = `/api/subscriptions/${subscription_id}/batches/export`;
    return this.http
      .get(exportEndpoint)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * get batch status before approval
   * @param subscription_id
   * @param batch_id
   * @returns
   */
  getBatchStatusBeforeApproval = (subscription_id: string, batch_id: string) => {
    const endpoint = this.globals.urlJoinWithTwoParam(
      'home',
      'batchStatus',
      subscription_id,
      batch_id
    );

    return this.http.get(endpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get list for file
   * @param subscription_id
   * @param batch_id
   * @param file_type
   * @returns
   */
  getListForFile = (subscription_id: string, batch_id: string, file_type: string): Observable<any> => {
    const endpoint = this.globals.urlJoin('home', 'singleBatchList');
    return this.http
      .get(
        endpoint +
          subscription_id +
          '/batches/' +
          batch_id +
          '/files/' +
          file_type +
          '/list'
      )
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
