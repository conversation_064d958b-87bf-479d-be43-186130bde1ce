import { Injectable } from '@angular/core';
// import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root',
})
export class SnackbarService {
  // constructor(private snackBar: MatSnackBar) {}

  openSnackBar = (message: any, action: string) => {
    console.log(message);
    if (message != 'login_required') {
      if (typeof message == 'string') {
       console.log(message);

      } else if (!message || !message.detail) {
        alert(`An unexpected error has occurred. Please contact dataX support.`);
      } else {
        console.log(message.detail);

      }
    }
  };
}
