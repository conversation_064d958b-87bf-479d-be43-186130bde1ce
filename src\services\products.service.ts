import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Globals } from '../app/_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ENDPOINTS } from '../app/_globals/endpoints';

@Injectable({
  providedIn: 'root',
})
export class ProductsService {
  private endpoints: any = ENDPOINTS;
  private httpOptions: HttpHeaders;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get product table headers
   * @param module_slug
   * @returns
   */
  getProductHeader = (subs_id: string) => {
    const productHeaderListEndpoint = this.globals.urlJoinWithParam(
      'products',
      'productHeaders',
      subs_id
    );
    return this.http.get(productHeaderListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get Products table
   * @param subs_id
   * @param page
   * @param size
   * @param bucket
   * @param search_query
   * @param start_date
   * @param end_date
   * @returns
   */
  getProductList = (
    subs_id: string,
    page: string,
    size: string,
    bucket: string,
    search_query: string[],
    filter: string[],
    start_date: string,
    end_date: string
  ): Observable<any> => {
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('bucket', bucket)
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search_query.forEach((item: string) => {
      options.params = options.params.append('q', encodeURIComponent(item));
    });
    filter.forEach((item: string) => {
      options.params = options.params.append('filter', item);
    });
    const ProductListEndpoint = this.globals.urlJoinWithParam(
      'products',
      'productList',
      subs_id
    );
    return this.http.get(ProductListEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * each bucket count
   * @param subs_id
   * @returns
   */
  getBucketCount = (subs_id: string, search_query: string[], filter: string[], start_date: string, end_date: string) => {
    const bucketCountEndpoint = this.globals.urlJoinWithParam(
      'products',
      'bucketCount',
      subs_id
    );
    const options = {
      params: new HttpParams()
        .set('start_date', start_date)
        .set('end_date', end_date),
    };
    search_query.forEach((item: string) => {
      options.params = options.params.append('q', encodeURIComponent(item));
    });
    filter.forEach((item: string) => {
      options.params = options.params.append('filter', item);
    });
    return this.http.get(bucketCountEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get is_batch boolean and progress for each filter
   * @param module
   * @param filters
   * @returns
   */

  getBatchProgress = (subs_id: string, q: string[]): Observable<any> => {
    const batchProgressEndpoint = this.globals.urlJoinWithParam(
      'products',
      'batchProgress',
      subs_id
    );
    const options = {
      params: new HttpParams(),
    };
    q.forEach((item: string) => {
      options.params = options.params.append('q[]', encodeURIComponent(item));
    });
    return this.http.get(batchProgressEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * move row to specific bucket
   * @param batchId
   * @param obj
   * @param subs_id
   * @returns
   */
  bucketUpdate = (rowId: string, obj: any, subs_id: string) => {
    const buckUpdateEndpoint = this.globals.urlJoinWithTwoParam(
      'products',
      'bucketUpdate',
      subs_id,
      rowId
    );
    return this.http.patch(buckUpdateEndpoint, obj, {}).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
}
