<div class="header-container">
    <cax-button label="Raise a Ticket" (click)="createTicket()"></cax-button>
</div>
<div class="ticket-list-container">List here</div>

<cax-sidebar
    [headerText]="'Create Ticket'"
    showCloseIcon="true"
    position="right"
    [style]="{ width: '50%' }"
    [(visible)]="ticketSidebarVisible">
    <div class="sidebar-content">
    <cax-inputtext
                [(ngModel)]="newTicket.title"
                [value]="''"
                [placeholder]="'Placeholder'"
                [disabled]="false"
                [clearIcon]="true"
                [label]="'Title'"
                [invalid]="false"
                [style]="{width: '100%'}"
                [size]="'md'">
    </cax-inputtext>
    <div class="description">
    <p class="text">Description</p>
    <cax-editor
        [style]="{ minHeight: '200px' }"
        [placeholder]="'Editor with custom height'"
         (onTextChange)="handleTextChange($event)">
    </cax-editor>
    </div>
    <cax-upload 
            [maxFileSize]="30">
    </cax-upload>
    <div class="button-container">
            <cax-button
                class="half-width-button"
                [label]="'Create'"
                [outlined]="false"
                severity="primary"  
                [style]="{ width: '100%' }"
                (click)="creatTicket()"
                >
            </cax-button>
            <cax-button
                class="half-width-button"
                [label]="'Cancel'"
                [style]="{ width: '100%' }"
                severity="secondary"
                (click)="cancleTicket()">
            </cax-button>
        </div>
    </div>
</cax-sidebar>
