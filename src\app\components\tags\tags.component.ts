import {
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewChild,
    ChangeDetectionStrategy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'cax-design-system/table';
import { ButtonModule } from 'cax-design-system/button';
import { InputSwitchModule } from 'cax-design-system/inputswitch';
import { PaginatorModule } from 'cax-design-system/paginator';
import { ChipModule } from 'cax-design-system/chip';
import { DialogModule } from 'cax-design-system/dialog';
import { OverlayPanelModule } from 'cax-design-system/overlaypanel';
import { TimelineModule } from 'cax-design-system/timeline';
import { ConfirmationService } from 'cax-design-system/api';
import { ConfirmDialogModule } from 'cax-design-system/confirmdialog';
import { ToggleSwitchModule } from 'cax-design-system/toggleswitch';
// Define Tag interface locally since we removed static data
interface Tag {
    id: string;
    name: string;
    description: string;
    customStyle?: {
        backgroundColor: string;
        color: string;
    };
    isActive: boolean;
    dateCreated: Date;
    createdBy: string;
    dateModified: Date;
    modifiedBy: string;
}
import { InputTextModule } from 'cax-design-system/inputtext';
import { SidebarModule } from 'cax-design-system/sidebar';
import { CreateNewTagSidebarComponent } from './create-new-tag-sidebar/create-new-tag-sidebar.component';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
    selector: 'app-tags',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TableModule,
        ButtonModule,
        InputSwitchModule,
        PaginatorModule,
        ChipModule,
        DialogModule,
        OverlayPanelModule,
        TimelineModule,
        ConfirmDialogModule,
        ToggleSwitchModule,
        InputTextModule,
        SidebarModule,
        CreateNewTagSidebarComponent,
    ],
    templateUrl: './tags.component.html',
    styleUrl: './tags.component.scss',
    providers: [ConfirmationService],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagsComponent implements OnInit {
    @ViewChild('tagHistoryPanel') tagHistoryPanel: any;

    tags: Tag[] = [];
    columns: any[] = [];
    selectedTags: Tag[] = [];
    tagHistoryList: string[] = [];
    searchQuery: string = '';
    filteredTags: Tag[] = [];
    tagSidebarVisible: boolean = false;
    selectedTag: Tag | null = null;

    constructor(
        private confirmationService: ConfirmationService,
        private cdr: ChangeDetectorRef,
    ) {}

    ngOnInit() {
        // Load all data at once - replace with API call
        this.tags = []; // TODO: Load from API
        this.filteredTags = [...this.tags];

        this.columns = [
            { field: 'name', header: 'Tag' },
            { field: 'description', header: 'Description' },
            { field: 'dateModified', header: 'Date modified' },
            { field: 'modifiedBy', header: 'Modified By' },
            { field: 'dateCreated', header: 'Date created' },
            { field: 'createdBy', header: 'Created By' },
            { field: 'status', header: 'Status' },
        ];
    }

    /**
     * Get the display status for a tag
     */
    getStatusDisplay(tag: Tag): string {
        return tag.isActive ? 'Active' : 'Inactive';
    }

    showTagHistory(event: any, tag: Tag) {
        event.stopPropagation();

        if (this.tagHistoryPanel) {
            this.tagHistoryPanel.hide();
        }
        this.tagHistoryList = [];

        if (tag.history && tag.history.length > 0) {
            setTimeout(() => {
                this.tagHistoryList = tag.history ? [...tag.history] : [];

                if (this.tagHistoryPanel) {
                    this.tagHistoryPanel.show(event);
                }

                this.cdr.detectChanges();
            }, 200);
        }
    }

    /**
     * Handle toggle switch change event
     */
    onStatusToggle(event: MouseEvent, tag: Tag): void {
        event.preventDefault();
        event.stopPropagation();

        if (tag.isActive) {
            this.confirmationService.confirm({
                message: `This tag will be removed from all the batches. Are you sure you want to deactivate this tag?`,
                header: `Deactivating '${tag.name}'?`,
                acceptLabel: 'Yes, deactivate',
                rejectLabel: 'Cancel',
                acceptButtonStyleClass: 'cax-button-danger',
                rejectButtonStyleClass:
                    'cax-button-secondary cax-button-outlined',
                headerIcon: 'cax cax-close-circle',
                headerIconStyle: { color: 'var(--error-500)' },
                accept: () => {
                    tag.isActive = false;

                    if ('status' in tag) {
                        tag.status = 'Inactive';
                    }

                    this.cdr.detectChanges();
                },
                reject: () => {
                    this.cdr.detectChanges();
                },
            });
        } else {
            tag.isActive = true;

            if ('status' in tag) {
                tag.status = 'Active';
            }
            this.cdr.detectChanges();
        }
    }

    onSearch() {
        const query = this.searchQuery.toLowerCase();
        if (!query) {
            this.filteredTags = [...this.tags];
        } else {
            this.filteredTags = this.tags.filter(
                tag =>
                    tag.name.toLowerCase().includes(query) ||
                    (tag.description &&
                        tag.description.toLowerCase().includes(query))
            );
        }
        this.cdr.detectChanges();
    }

    openCreateTagSidebar() {
        this.selectedTag = null;
        this.tagSidebarVisible = true;
    }

    onCreateNewTag(newTag: any) {
        const tag: Tag = {
            id: this.generateNewId(),
            name: newTag.name,
            description: newTag.description,
            severity: 'custom',
            dateCreated: new Date().toLocaleDateString(),
            createdBy: 'Current User',
            dateModified: new Date().toLocaleDateString(),
            modifiedBy: 'Current User',
            isActive: true,
            isRemovable: true,
            history: [],
            customStyle: newTag.customStyle,
        };

        this.tags.unshift(tag);
        this.filteredTags = [...this.tags];
        this.onSidebarClose();
    }

    onTagClick(tag: Tag) {
        this.selectedTag = tag;
        this.tagSidebarVisible = true;
        this.cdr.detectChanges();
    }

    onSidebarClose() {
        this.tagSidebarVisible = false;
        this.selectedTag = null;
        this.cdr.detectChanges();
    }

    onTagUpdated(updatedTag: Tag) {
        const index = this.tags.findIndex(t => t.id === updatedTag.id);
        if (index !== -1) {
            const existingTag = this.tags[index];
            this.tags[index] = {
                ...existingTag,
                name: updatedTag.name,
                description: updatedTag.description,
                severity: updatedTag.severity,
                customStyle: updatedTag.customStyle,
                dateModified: new Date().toLocaleDateString(),
                modifiedBy: 'Current User',
                id: existingTag.id,
                dateCreated: existingTag.dateCreated,
                createdBy: existingTag.createdBy,
                isActive: existingTag.isActive,
                isRemovable: existingTag.isRemovable,
                status: existingTag.status,
                history: existingTag.history ? [...existingTag.history] : [],
            };
            this.filteredTags = [...this.tags];
            this.selectedTag = null;
            this.cdr.detectChanges();
        }
    }

    private generateNewId(): string {
        return (this.tags.length + 1).toString().padStart(2, '0');
    }
}
