.filters-container {
    padding: 4px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .filter-count {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        font-size: 18px;
    }

    .filter-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        li {
            list-style-type: none;
            padding: 6px 8px;
            display: flex;
            gap: 8px;
            align-items: center;
            justify-content: space-between;

            div {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .filter-column {
                    font-weight: 600;
                    font-size: 14px;
                }

                .filter-type {
                    font-weight: 400;
                    font-size: 12px;
                    color: var(--neutral-750);
                }
            }
        }
    }
}
