<app-loading *ngIf="auth.isLoading$ | async; else loaded"></app-loading>
<ng-template #loaded>
  <cax-navigation
    (onNavExpanded)="navBarStatus($event)"
    [activeTab]="activeTab"
    [header]="'Content Enrichment'"
    [topNavList]="topNavList"
    [bottomNavList]="bottomNavList"
    [notifications]="true"
    [helpCentre]="true"
    [version]="'V.2.1'"
    [copyrightYear]="2025"
    [userName]="'<PERSON>'"
    [subscriptionMode]="'advance'"
    [subscriptionList]="['Team A', 'Team B']"
    (onNavListItemChange)="changeActiveItem($event)"
    (onHelpCentreClick)="openHelpCentre()"
    [advanceSubscriptionList]="[
      { name: 'AD', subscription: ['Team A', 'Team B'] },
      { name: 'SPI', subscription: ['Team C', 'Team D'] }
    ]"
  />

  <div class="main-content" [ngClass]="{ expanded: navBarExpanded }">
    <router-outlet></router-outlet>
  </div>
</ng-template>
