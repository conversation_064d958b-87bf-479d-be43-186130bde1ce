import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'cax-design-system/inputtext';
import { ToggleSwitchModule } from 'cax-design-system/toggleswitch';

@Component({
    selector: 'app-column-list-sidebar',
    standalone: true,
    imports: [InputTextModule, FormsModule, ToggleSwitchModule, CommonModule],
    templateUrl: './column-list-sidebar.component.html',
    styleUrl: './column-list-sidebar.component.scss',
})
export class ColumnListSidebarComponent {
    @Input() columnListData: any[] = [];
    columnListFilter: string = '';

    @Output() emitFilteredColumns: EventEmitter<any> = new EventEmitter<any>();

    get filteredColumns() {
        return this.columnListData.filter(column =>
            column.header
                .toLowerCase()
                .includes(this.columnListFilter.toLowerCase())
        );
    }

    onToggleChange(event: any, col: any) {
        this.columnListData
            .filter(i => i.field == col.field)
            .map(j => (j.selected = event));
        this.emitFilteredColumns.emit(this.columnListData);
    }
}
