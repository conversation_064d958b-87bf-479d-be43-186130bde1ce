.action-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    gap: 16px;

    .search-container {
        flex: 1;
        max-width: 560px;
    }

    .button-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
}

.table-container {
    padding: 0 24px;
}

.paginator-container {
    padding: 16px 24px;
}

.batch-details {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-list {
        display: flex;
        align-items: center;
        gap: 24px;
        .info-column-name {
            width: 120px;
            font-weight: 500;
            color: var(--neutral-500);
        }
        .info-column-text {
            font-weight: 500;
        }
    }

}

.comment-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.comments-container {
    display: flex;
    cursor: pointer;
    height: 100%;
}

.selection-sidebar-content {
    display: flex;
    flex-direction: column;

    .fields-container {
        display: flex;
        align-items: flex-end;
        gap: 16px;
        width: 100%;
    }

    .input-container {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .button-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .divider {
        height: 42px;
        width: 1px;
        background-color: var(--neutral-100);
    }

}

.data-container {
    .batch-name {
        color: var(--primary-color);
        font-weight: 500;
        gap: 6rem;
        span {
            cursor: pointer;
        }
        i {
        
            cursor: pointer;
        }
    }
}

::ng-deep .cax-dialog .cax-dialog-content {
    padding: 0px;
}

// Header content styles
.search-attribute{
    max-width: 427px;
}
.custom-dialog-header {
  justify-content: space-between;
}

.custom-dialog-left {
  justify-content: space-between;
  gap: 1rem;
}

.custom-dialog-name {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.batch-id {
  font-weight: 700;
  font-size: 18px;
}

.cax-icons {
  font-size: 20px;
}

.icon-right {
  gap: 1rem;
}

/* Main content area - Full height layout */
.dialog-content-flex {
  display: flex;
  height: 100%;
  overflow: hidden;
  //padding: 0px !important;
}

/* Left side content (toolbar + form) */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  //transition: all 0.3s ease;
  overflow: hidden;
}

.main-content.with-log {
  flex: 0 0 70%;
}

/* Top-level toolbar styles */
.top-level {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  padding: 1rem;
  flex-shrink: 0;
}

.left-section {
  flex: 1 1 60%;
}

.right-section {
  flex: 1 1 40%;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

/* Form wrapper */
.form-wrapper {
  flex: 1;
  overflow-y: auto;
}

.main-content.with-log .form-field {
  flex: 1 1 calc(50% - 1rem);
}

.form-wrapper {
    padding: 20px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-field label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.input-container input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.input-container.is-readonly input {
    color: #666;
    background-color: #F6F7F8 !important;
    cursor: not-allowed;
}

.input-container.is-readonly input:focus {
    border-color: #ddd;
    box-shadow: none;
}

.lock-icon {
    position: absolute;
    right: 12px;
    color: #666;
    font-size: 16px;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 1;
}

.input-container.has-value:hover .lock-icon {
    opacity: 1;
}

/* Lock icon only visible on hover */
.input-container.has-value .lock-icon {
    opacity: 0;
}

.input-container.has-value:hover .lock-icon {
    opacity: 1;
}
/* Responsive design */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}

/* Sidebar style - Full height */
.log-sidebar {
  flex: 0 0 30%;
  background-color: #F6F7F8;
  border-left: 1px solid #C6CAD2;
  display: flex;
  flex-direction: column;


  .heading{
    font-size: 24px;
    padding-left: 1rem;
    padding-right: 1rem;
    font-weight: 600;
    color: #16181D;

  }
}

.top-level .button {
  display: flex;
  gap: 1rem;
}

/* Responsive behavior */
@media (max-width: 1200px) {
  .form-field {
    flex: 1 1 calc(50% - 1rem);
  }
}

@media (max-width: 768px) {
  .dialog-content-flex {
    flex-direction: column;
  }

  .main-content.with-log,
  .main-content {
    flex: 1 1 100%;
  }

  .form-field {
    flex: 1 1 100%;
  }

  .log-sidebar {
    flex: 0 0 40%;
    border-left: none;
    border-top: 1px solid #ccc;
    margin: 0% ;
  }
}
.line{
    border: 1px solid #C6CAD2;
    padding: 0px !important;
}
.timeline-data{
    background-color: #F6F7F8;
    padding: 1rem;
}
.timeline-entry {
  margin-top: -1rem;
  margin-bottom: 1rem;
}

.user-date {
  margin-bottom: 0.5rem;
  font-size: 14px;
  color: #0B0C0E;
  font-weight: 500;
}

.change-list {
  padding-left: 1.2rem;
  margin: 0;
  font-size: 13px;
  background-color: white;
  color: 400;
}

/* Document List Styles */
.main-heading{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 1rem;
    padding-right: 1rem;
}
.document-heading{
    font-size: 16px;
    font-weight: 600;
}
.document-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px; 
  padding: 0.5rem;
}
.document-name {
  font-size: 14px;
  color: #16181D;
  display: block;
  font-weight: 500;
  border-radius: 8px;
  background-color: #ffffff;
  padding : 1rem;

}

/* Action Buttons Container */
.chat-actions {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  //gap: 2rem;
  border-top: 1px solid #ccc;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.upload-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  transition: color 0.3s ease;
}
.image-preview-container {
  display: flex;
  justify-content: center;
  padding: 1rem ;
}
.image-container{
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
}

.image-preview {
  max-width: 100%;
  height: auto;
  max-height: 250px;
  border-radius: 8px;
  object-fit: contain;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  background-color: white;
}

.disabled-icon {
  opacity: 0.3;
  pointer-events: none;
}

.comment-contaiener {
  flex: 1;
  overflow-y: auto;
  padding: 12px 16px;
}


