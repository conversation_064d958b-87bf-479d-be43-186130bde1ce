import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ButtonModule } from 'cax-design-system/button';
import { PaginatorModule } from 'cax-design-system/paginator';
import { SplitButtonModule } from 'cax-design-system/splitbutton';
import { TableModule } from 'cax-design-system/table';
import { BadgeModule } from 'cax-design-system/badge';
import { AutoCompleteModule } from 'cax-design-system/autocomplete';
import { TableconfigurationModule } from 'cax-design-system/tableconfiguration';
import { OverlayPanelModule } from 'cax-design-system/overlaypanel';
import { SidebarModule } from 'cax-design-system/sidebar';
import { ColumnListSidebarComponent } from '../column-list-sidebar/column-list-sidebar.component';
import { FiltersListPanelComponent } from '../filters-list-panel/filters-list-panel.component';
import { DividerModule } from 'cax-design-system/divider';
import { ChipModule } from 'cax-design-system/chip';
import { AvatarModule } from 'cax-design-system/avatar';
import { InputTextModule } from 'cax-design-system/inputtext';
import { CommentboxModule } from 'cax-design-system/commentbox';
import { ConfirmationService, MessageService } from 'cax-design-system/api';
import { ConfirmDialogModule } from 'cax-design-system/confirmdialog';
import { TimelineModule } from 'cax-design-system/timeline';
import { DialogModule } from 'cax-design-system/dialog';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'cax-design-system/dropdown';

// Service imports
import { HomeService } from '../../../../services/home.service';
import { CommentsService } from '../../../../services/comments.service';
import { ProductsService } from '../../../../services/products.service';
import { ProductDetailsService } from '../../../../services/product-details.service';

@Component({
    selector: 'app-batch-rows',
    standalone: true,
    imports: [
        ButtonModule,
        TableModule,
        SplitButtonModule,
        PaginatorModule,
        CommonModule,
        BadgeModule,
        AutoCompleteModule,
        TableconfigurationModule,
        OverlayPanelModule,
        SidebarModule,
        ColumnListSidebarComponent,
        FiltersListPanelComponent,
        DividerModule,
        ChipModule,
        AvatarModule,
        InputTextModule,
        CommentboxModule,
        ConfirmDialogModule,
        TimelineModule,
        DialogModule,
        AutoCompleteModule,
        FormsModule,
        DropdownModule,
    ],
    templateUrl: './batch-rows.component.html',
    styleUrl: './batch-rows.component.scss',
    providers: [ConfirmationService, MessageService],
})
export class BatchRowsComponent implements OnInit {
    @Input() batchDetails: any;
    skuFontSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    skuRowSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    avatarSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    size: 'sm' | 'md' | 'lg' = 'md';
    skuColumnHeaders: any[] = [];
    skuList: any[] = [];
    attributeList: any[] = [];
    columnSidebarVisible: boolean = false;
    batchInfoSidebarVisible: boolean = false;
    commentsSidebarVisible: boolean = false;
    openComment: boolean = false;
    currentBatchComments: any[] = [];
    currentBatchId: string = '';
    currentBatchName: string = '';
    currentBatchHeader: string = '';
    selectedBatchesComment: string = '';
    selectedBatchesCategory: string = '';
    selectedBatches: any[] = [];

    // API-driven data
    mentionSuggestions: any[] = [];
    hashtagSuggestions: any[] = [];
    batchStatusList: any[] = [];
    selectionSidebarVisible: boolean = false;
    selectedBatchStatus: string = '';
    statusOptions: any[] = [];

    // Subscription management
    subscriptionId: string = '';

    // Loading states
    isLoading: boolean = false;
    batchLogList: any[] = [];
    batchDetailsDialogVisible: boolean = false;
    //selectedBatch: any ;
    logVisible: boolean = false;
    chatVisible: boolean = false;
    selectedOption: any = null;
    saveConfirmationVisible: boolean = false;
    selectedFieldForUpload: any;
    filteredDocuments: any[] = [];
    selectedBatchIndex: number = 0; // set when opening the dialog
    batchList: any[] = [];
    commentsVisible = false;
    @ViewChild('caxComments') caxCommentsComponent: any;
    commentsVisibleData = true;
    commentsRendered = false;
    showCommentSection = false;
    sidebarHeader = 'Comments';

    @ViewChild('batchLogPanel') batchLogPanel: any;
    @Input() selectedBatch!: any;

    constructor(
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private homeService: HomeService,
        private commentsService: CommentsService,
        private productsService: ProductsService,
        private productDetailsService: ProductDetailsService
    ) {}

    // API-driven data
    mockLogs: any[] = [];
    chipOptions: any[] = [];
    commentsData: any[] = [];

    // API-driven suggestion data
    hashtagSuggestionsData: any[] = [];
    mentionSuggestionsData: any[] = [];

    ngOnInit() {
        this.subscriptionId = localStorage.getItem('SubscriptionID') || '';
        this.loadInitialData();
    }

    private loadInitialData() {
        if (!this.subscriptionId) {
            console.error('No subscription ID available');
            return;
        }

        this.isLoading = true;

        // Load product headers and data
        Promise.all([
            this.loadProductHeaders(),
            this.loadProductList(),
            this.loadAttributeList()
        ]).finally(() => {
            this.isLoading = false;
        });
    }

    private loadProductHeaders() {
        return this.productsService.getProductHeader(this.subscriptionId).subscribe({
            next: (response) => {
                this.skuColumnHeaders = response.map((header: any) => ({
                    ...header,
                    selected: true
                }));
            },
            error: (error) => {
                console.error('Error loading product headers:', error);
                this.skuColumnHeaders = [];
            }
        });
    }

    private loadProductList() {
        return this.productsService.getProductList(
            this.subscriptionId,
            '1', // page
            '50', // size
            'ALL', // bucket
            [], // search_query
            [], // filter
            '', // start_date
            '' // end_date
        ).subscribe({
            next: (response) => {
                this.skuList = response.result || [];
            },
            error: (error) => {
                console.error('Error loading product list:', error);
                this.skuList = [];
            }
        });
    }

    private loadAttributeList() {
        return this.productDetailsService.getAttributeList(this.subscriptionId).subscribe({
            next: (response) => {
                this.attributeList = response || [];
            },
            error: (error) => {
                console.error('Error loading attribute list:', error);
                this.attributeList = [];
            }
        });
    }

    private loadProductDetails(rowId: string) {
        return this.productDetailsService.getProductDetails(
            this.subscriptionId,
            rowId,
            'ALL', // bucket
            [], // search_queries
            [], // product_list
            '', // start_date
            '' // end_date
        ).subscribe({
            next: (response) => {
                // Handle product details response
                console.log('Product details loaded:', response);
                return response;
            },
            error: (error) => {
                console.error('Error loading product details:', error);
                return null;
            }
        });
    }

    saveEditedResponse(rowId: string, editedData: any) {
        return this.productDetailsService.patchEditedResponse(
            this.subscriptionId,
            rowId,
            editedData
        ).subscribe({
            next: (response) => {
                console.log('Edited response saved:', response);
                // Reload product list to get updated data
                this.loadProductList();
            },
            error: (error) => {
                console.error('Error saving edited response:', error);
            }
        });
    }

    onRowHeightChange(event: 'sm' | 'md' | 'lg' | 'xl') {
        this.skuRowSize = event;
        this.avatarSize = event;
        this.size = event === 'xl' ? 'lg' : event;
    }

    onFontSizeChange(event: 'sm' | 'md' | 'lg' | 'xl') {
        this.skuFontSize = event;
    }

    get sortedSelectedColumns() {
        return this.skuColumnHeaders
            .filter((column: any) => column.selected)
            .sort((a: any, b: any) => a.order - b.order);
    }

    getBatchStatus(key: string) {
        return (
            this.batchStatusList.find((status: any) => status.key === key) || {
                name: 'In Queue',
                key: 'in_queue',
                icon: 'cax cax-question-circle',
                severity: 'secondary',
            }
        );
    }

    openBatchStatusPanel(event: any, status: any, rowIndex: number) {
        console.log('this is cliscked');
    }

    getBatchesToUncheck(): string[] {
        return this.selectedBatches.map(batch => batch.batch_id);
    }

    onTableSelectionChange(event: any) {
        this.selectedBatches = event;
        this.selectionSidebarVisible = this.selectedBatches.length > 0;

        // Clear input fields when selection changes
        if (this.selectedBatches.length > 0) {
            this.selectedBatchStatus = '';
            // this.selectedBatchETA = '';
            // this.selectedBatchAssignee = '';
            this.selectedBatchesComment = '';
            this.selectedBatchesCategory = '';
        }
    }

    saveAllChanges() {
        console.log('this is clicked');
        // Check if any changes have been made
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasCategory =
            !!this.selectedBatchesCategory &&
            this.selectedBatchesCategory.trim() !== '';
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        if (!hasStatusChange && !hasCategory && !hasCommentChange) {
            return;
        }
        const batchesToUncheck = this.getBatchesToUncheck();

        // Show confirmation dialog
        this.confirmationService.confirm({
            message: `Are you sure you want to apply changes to ${this.selectedBatches.length} selected batches?`,
            header: 'Confirm Changes',
            acceptLabel: 'Yes, Save Changes',
            rejectLabel: 'Cancel',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            closable: true,
            headerIcon: 'cax cax-info-circle',
            headerIconStyle: { color: 'var(--primary-500)' },
            accept: () => {
                // Apply all changes
                if (hasStatusChange) {
                    this.updateMultipleBatchStatus(this.selectedBatchStatus);
                }
                if (hasCategory) {
                    this.onMultipleCommentsAdded(this.selectedBatchesCategory);
                }

                if (hasCommentChange) {
                    this.onMultipleCommentsAdded(this.selectedBatchesComment);
                }
                this.selectedBatchStatus = '';
                this.selectedBatchesCategory = '';
                this.selectedBatchesComment = '';
                console.log('Changes saved successfully!');
                this.uncheckSelectedItems(batchesToUncheck);
                this.selectedBatches = [];
                this.selectionSidebarVisible = false;
            },
        });
    }

    isSaveEnabled(): boolean {
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasCategory =
            !!this.selectedBatchesCategory &&
            this.selectedBatchesCategory.trim() !== '';
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        return hasStatusChange || hasCommentChange || hasCategory;
    }

    uncheckSelectedItems(batchIds: string[]) {
        this.selectedBatches = this.selectedBatches.filter(
            batch => !batchIds.includes(batch.batch_id)
        );

        // Hide selection sidebar if no items remain selected
        if (this.selectedBatches.length === 0) {
            this.selectionSidebarVisible = false;
        }
    }

    updateMultipleBatchStatus(status: string) {
        if (!status) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.skuList.findIndex(
                item => item.batch_id === batch.batch_id
            );

            if (batchIndex !== -1) {
                this.skuList[batchIndex].status = status;
            }
        });
    }

    filterColumnsList(event: any) {
        this.skuColumnHeaders = event;
    }

    openColumnsList() {
        this.columnSidebarVisible = true;
    }

    openBatchInfo() {
        this.batchInfoSidebarVisible = true;
    }

    openComments(rowData: any) {
        this.currentBatchId = rowData.row_id;
        this.currentBatchName = rowData.name;
        this.currentBatchHeader = `${rowData.brand_name} (${rowData.row_id})`;
        this.currentBatchComments = rowData.comments || [];
        this.commentsSidebarVisible = true;
        this.openComment = true;
    }

    onCommentAdded(comment: any) {
        const batchIndex = this.skuList.findIndex(
            batch => batch.row_id === this.currentBatchId
        );
        if (batchIndex !== -1) {
            // Add the new comment to the batch
            if (!this.skuList[batchIndex].comments) {
                this.skuList[batchIndex].comments = [];
            }

            this.skuList[batchIndex].comments.push({
                rawText: comment.text,
                date: new Date(),
                sender: 'Current User',
                isAdmin: false,
            });
        }
    }

    onMultipleCommentsAdded(comment: string) {
        if (!comment || !comment.trim()) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.skuList.findIndex(
                item => item.row_id === batch.row_id
            );

            if (batchIndex !== -1) {
                // Add the new comment to the batch
                if (!this.skuList[batchIndex].comments) {
                    this.skuList[batchIndex].comments = [];
                }

                this.skuList[batchIndex].comments.push({
                    text: comment,
                    date: new Date(),
                    sender: 'Current User',
                    isAdmin: false,
                });
            }
        });

        // Clear the comment input
        this.selectedBatchesComment = '';
        this.selectedBatchesCategory = '';
    }
    openBatchDetails(batch: any): void {
        this.batchList = this.skuList;
        this.selectedBatchIndex = this.batchList.findIndex(
            b => b.row_id === batch.row_id
        );
        this.selectedBatch = batch;
        this.batchDetailsDialogVisible = true;
        // Load attribute list for this specific batch/row
        this.loadAttributeList();
    }

    openBatchLogPanel(event: any, batchData: any) {
        this.batchLogPanel.hide();
        setTimeout(() => {
            this.batchLogList = batchData.audit_log;
            this.batchLogPanel.show(event);
        }, 200);
        console.log('this is clicked');
    }

    toggleChat() {
        if (this.chatVisible) {
            this.chatVisible = false;
        } else {
            this.chatVisible = true;
            this.logVisible = false;
            this.commentsVisible = false;
        }
    }

    toggleLog() {
        if (this.logVisible) {
            this.logVisible = false;
        } else {
            this.logVisible = true;
            this.chatVisible = false;
            this.commentsVisible = false;
        }
    }

    onCommentsVisibleChange(val: boolean) {
        this.commentsVisible = val;
        if (!val) {
            this.commentsRendered = false;
        }
    }
    toggleComments() {
        console.log('this si comment');
        this.commentsVisible = !this.commentsVisible;
        // Close other sidebars if needed
        console.log('New state:', this.commentsVisible);
        if (this.commentsVisible) {
                this.logVisible = false;
                this.chatVisible = false;

    setTimeout(() => {
      this.commentsRendered = true;

      setTimeout(() => {
                if (this.caxCommentsComponent) {
                this.caxCommentsComponent.visible = true;
                }
            }, 50); // wait for DOM init
            }, 100);
        } else {
            this.commentsRendered = false;
        }
    }
    openDocument(doc: any) {
        console.log('Opening document:', doc.name);
    }

    showSaveConfirmation(event: Event) {
        const savedPreference = localStorage.getItem('skipDialog') ?? 'false';

        if (savedPreference === 'false') {
            this.confirmationService.confirm({
                target: event.target as EventTarget, // Needed for popover-style dialog
                message:
                    'You have unsaved changes. Are you sure you want to exit?',
                header: 'Are you sure ?',
                headerIcon: 'cax cax-close-circle',
                acceptLabel: 'Yes',
                rejectLabel: 'Cancel',
                acceptButtonStyleClass: 'cax-button-danger',
                rejectButtonStyleClass: 'cax-button-secondary ',
                headerIconStyle: {
                    color: 'red',
                },
                closable: true,
                showCheckbox: false,
                accept: () => {
                    this.messageService.add({
                        severity: 'info',
                        summary: 'Confirmed',
                        detail: 'You have accepted',
                    });
                },
                reject: () => {
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Rejected',
                        detail: 'You have rejected',
                    });
                },
            });
        }
    }

    // for the uplaod of the image and the document
    isDocumentOrImageField(label: string): boolean {
        const lowerLabel = label.toLowerCase();
        return lowerLabel.includes('document') || lowerLabel.includes('image');
    }

    openChatForUpload(field: any): void {
        this.selectedFieldForUpload = field;
        this.chatVisible = true;
        this.logVisible = false;
        //this.filterDocumentsForField(field);

        console.log('Opening upload for field:', field.label);
    }

    getFieldType(label: string): 'image' | 'document' | 'other' {
        const lowerLabel = label.toLowerCase();
        if (lowerLabel.includes('image')) return 'image';
        if (lowerLabel.includes('document')) return 'document';
        return 'other';
    }

    isImageFile(filename: string): boolean {
        const imageExtensions = [
            '.jpg',
            '.jpeg',
            '.png',
            '.gif',
            '.bmp',
            '.svg',
            '.webp',
        ];
        return imageExtensions.some(ext =>
            filename.toLowerCase().endsWith(ext)
        );
    }

    isDocumentFile(filename: string): boolean {
        const docExtensions = [
            '.pdf',
            '.doc',
            '.docx',
            '.xls',
            '.xlsx',
            '.ppt',
            '.pptx',
            '.txt',
        ];
        return docExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    }

    replaceDocument(): void {
        console.log(
            'Replacing document for:',
            this.selectedFieldForUpload?.label
        );
    }

    downloadDocument(): void {
        console.log(
            'Downloading document for:',
            this.selectedFieldForUpload?.label
        );
        if (this.selectedFieldForUpload?.value) {
            console.log('Downloading file:', this.selectedFieldForUpload.value);
        }
    }

    isImageUrl(url: string): boolean {
        return !!url && /\.(jpeg|jpg|gif|png|bmp|svg|webp)$/i.test(url);
    }

    goToNextBatch(): void {
        const index = this.batchList.findIndex(
            b => b.row_id === this.selectedBatch.row_id
        );

        if (index < this.batchList.length - 1) {
            this.selectedBatch = this.batchList[index + 1];
            this.loadAttributeList();
            this.chatVisible = false;
            this.logVisible = false;
        }
    }

    goToPreviousBatch(): void {
        const index = this.batchList.findIndex(
            b => b.row_id === this.selectedBatch.row_id
        );
        if (index > 0) {
            this.selectedBatch = this.batchList[index - 1];
            this.loadAttributeList();
            this.chatVisible = false;
            this.logVisible = false;
        }
    }

    isPreviousDisabled(): boolean {
        const index = this.batchList.findIndex(
            b => b.row_id === this.selectedBatch?.row_id
        );
        return index <= 0;
    }

    isNextDisabled(): boolean {
        const index = this.batchList.findIndex(
            b => b.row_id === this.selectedBatch?.row_id
        );
        return index >= this.batchList.length - 1;
    }
}
