import { Injectable } from '@angular/core';
import { HttpHeaders, HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Globals } from '../app/_globals/endpoints.global';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class HelpService {
  private httpOptions: HttpHeaders;
  constructor(
    private globals: Globals,
    private http: HttpClient,
    private router: Router
  ) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   *
   * @returns Get user details
   */
  getUserDetails = (): Observable<any> => {
    const getUserEndpoint = this.globals.urlJoin('help', 'userDetails');
    return this.http.get(getUserEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * POST feedback
   * @param email
   * @param feedback
   * @returns
   */
  postFeedback = (email: string, feedback: string, sub_id: string): Observable<any> => {
    const FeedbackEndpoint = this.globals.urlJoinWithParam(
      'help',
      'feedback',
      sub_id
    );
    return this.http
      .post(FeedbackEndpoint, {
        email: email,
        feedback: feedback,
      })
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };
}
