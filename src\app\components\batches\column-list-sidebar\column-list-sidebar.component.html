<div class="column-list-container">
    <cax-inputtext
        [placeholder]="'Search'"
        [leftIcon]="true"
        [clearIcon]="true"
        [(ngModel)]="columnListFilter"
        [leftIconClass]="'cax cax-magnifier'"></cax-inputtext>
    <div
        *ngFor="let col of filteredColumns"
        class="d-flex align-center spc-btwn">
        <span>{{ col.header }}</span>
        <cax-toggleswitch
            [checked]="col.selected"
            [disabled]="col.fixed"
            (onChange)="onToggleChange($event, col)"></cax-toggleswitch>
    </div>
</div>
