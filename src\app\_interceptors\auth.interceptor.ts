import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError, switchMap, catchError } from 'rxjs';
import { AuthService } from '@auth0/auth0-angular';
import { Router } from '@angular/router';
import { Auth0Service } from '../../services/auth0.service';

export const authInterceptor: HttpInterceptorFn = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const auth = inject(AuthService);
  const router = inject(Router);
  const auth0Service = inject(Auth0Service);

  // Always set withCredentials for API requests
  let authReq = req.clone({
    setHeaders: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    withCredentials: true
  });

  // If Content-Range header is present, preserve it
  if (req.headers.has('Content-Range')) {
    const contentRange = req.headers.get('Content-Range') || '';
    authReq = authReq.clone({
      setHeaders: {
        'Content-Range': contentRange
      }
    });
  }

  // Only add Auth0 token for API requests
  if (req.url.includes('/api/')) {
    return auth.getAccessTokenSilently().pipe(
      switchMap((token: string) => {
        // Clone the request and add the authorization header
        const tokenReq = authReq.clone({
          setHeaders: {
            Authorization: `Bearer ${token}`
          }
        });

        return next(tokenReq);
      }),
      catchError((error: HttpErrorResponse) => {
        // Handle offline scenario
        if (!navigator.onLine) {
          router.navigate(['/offline'], {
            queryParams: { from: router.url },
          });
        }

        // Handle authentication errors
        if (error.status === 403 || error.status === 401) {
          localStorage.clear();
          auth0Service.logUserOut();
        }

        return throwError(() => error);
      })
    );
  }

  // For non-API requests, proceed without token
  return next(authReq).pipe(
    catchError((error: HttpErrorResponse) => {
      if (!navigator.onLine) {
        router.navigate(['/offline'], {
          queryParams: { from: router.url },
        });
      }

      if (error.status === 403 || error.status === 401) {
        localStorage.clear();
        auth0Service.logUserOut();
      }

      return throwError(() => error);
    })
  );
};
