<form [formGroup]="uploadForm" (ngSubmit)="onSubmit()" class="upload-batch-container">
    <!-- Batch Name -->
    <cax-inputtext
        formControlName="batchName"
        [label]="'Batch Name *'"
        [placeholder]="'Enter batch name'"
        [invalid]="!!(uploadForm.get('batchName')?.invalid && uploadForm.get('batchName')?.touched)"
        [errorText]="getFieldError('Batch Name')"></cax-inputtext>

    <!-- File Upload -->
    <div class="file-upload-section">
        <label class="upload-label">Upload File *</label>
        <cax-upload 
            [maxFileSize]="50"
            [allowedFileTypes]="['csv', 'xlsx', 'xls']"
            (onSelect)="onFileSelect($event)"
            (onRemove)="onFileRemove($event)"/>
        <div *ngIf="uploadForm.get('file')?.invalid && uploadForm.get('file')?.touched" class="error-message">
            File is required
        </div>
    </div>

    <!-- Template Selection -->
    <cax-dropdown
        formControlName="templateId"
        [labelText]="'Template *'"
        [style]="{ width: '100%' }"
        [placeholder]="'Select template'"
        [options]="templateOptions"></cax-dropdown>
    <div *ngIf="uploadForm.get('templateId')?.invalid && uploadForm.get('templateId')?.touched" class="error-message">
        {{ getFieldError('Template') }}
    </div>

    <!-- Tags -->
    <cax-chips
        [label]="'Tags (Optional)'"
        [style]="{ width: '100%' }"
        [placeholder]="'Add tags'"
        [value]="tags"
        (onAdd)="onTagAdd($event)"
        (onRemove)="onTagRemove($event)"></cax-chips>

    <!-- Description -->
    <cax-inputtextarea
        formControlName="description"
        [placeholder]="'Enter description (optional)'"
        [label]="'Description (Optional)'"
        [rows]="3"></cax-inputtextarea>

    <!-- Action Buttons -->
    <div class="button-container">
        <cax-button
            type="button"
            severity="secondary"
            label="Cancel"
            (click)="onCancel()"
            [disabled]="isUploading"></cax-button>
        <cax-button
            type="submit"
            label="Upload Batch"
            [disabled]="!isFormValid || isUploading"
            [loading]="isUploading"></cax-button>
    </div>

    <!-- Progress Bar -->
    <div *ngIf="isUploading" class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="uploadProgress"></div>
        </div>
        <span class="progress-text">{{ uploadProgress }}% uploaded</span>
    </div>
</form>
