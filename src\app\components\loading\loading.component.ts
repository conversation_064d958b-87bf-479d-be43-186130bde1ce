import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../../services/auth0.service';
import { take } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token: any;
  queryParam: any;
  subscriptionId: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.queryParam = params;
      this.subscriptionId = params['sub'] || localStorage.getItem('SubscriptionID');
      this.handleAuthentication();
    });
  }

  private handleAuthentication(): void {
    this.auth.isAuthenticated$.pipe(take(1)).subscribe((isAuthenticated) => {
      if (isAuthenticated) {
        this.handleAuthenticatedUser();
      } else {
        this.auth0.login();
      }
    });
  }

  private handleAuthenticatedUser(): void {
    // Get user profile and token
    this.auth.user$.pipe(take(1)).subscribe((user) => {
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
      }
    });

    // Get access token
    this.auth.getAccessTokenSilently().subscribe({
      next: (token) => {
        this.token = token;

        if (this.subscriptionId) {
          localStorage.setItem('SubscriptionID', this.subscriptionId);
          this.router.navigate(['/home'], { queryParams: { sub: this.subscriptionId } });
        }
      },
      error: () => {
        this.auth0.login();
      }
    });
  }
}
