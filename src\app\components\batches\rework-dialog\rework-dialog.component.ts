import { Component, EventEmitter, Output } from '@angular/core';
import { ButtonModule } from 'cax-design-system/button';
import { CheckboxModule } from 'cax-design-system/checkbox';
import { InputTextareaModule } from 'cax-design-system/inputtextarea';

@Component({
    selector: 'app-rework-dialog',
    standalone: true,
    imports: [CheckboxModule, InputTextareaModule, ButtonModule],
    templateUrl: './rework-dialog.component.html',
    styleUrl: './rework-dialog.component.scss',
})
export class ReworkDialogComponent {
    @Output() reworkDialogStatus: EventEmitter<boolean> =
        new EventEmitter<boolean>(true);

    closeReworkDialog() {
        this.reworkDialogStatus.emit(false);
    }
}
