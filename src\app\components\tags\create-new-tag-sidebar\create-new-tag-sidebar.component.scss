.sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.create-tag-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    overflow-y: auto;
    .color-pickers {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0px 24px;
        gap: 50px;
        background: var(--cax-surface-ground);
        border-radius: 8px;
        position: relative;

        .text-color-picker {
            flex: 1;
            max-width: 200px;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                right: -24px;
                top: 0;
                height: 100%;
                width: 1px;
                background-color: var(--neutral-300);
            }
        }

        .background-color-picker {
            flex: 1;
            max-width: 200px;
        }

        .text-color-picker,
        .background-color-picker {
            label {
                display: block;
                margin-bottom: 8px;
                font-size: 14px;
                font-weight: 500;
            }

            .color-value {
                margin-top: 8px;
                text-align: start;
                padding: 4px 12px;
                border: 1px solid var(--neutral-200);
                border-radius: 8px;
            }
        }
    }

    .tag-preview {
        .preview-heading {
            display: block;
            font-weight: 500;
            font-size: 14px;
        }

        .preview-container {
            display: flex;
            margin-top: 8px;
            justify-content: center;
            padding: 16px;
            border: 1px solid var(--neutral-200);
            border-radius: 8px;
        }
    }
}

.button-container {
    display: flex;
    gap: 16px;
    width: 100%;
}

.half-width-button {
    flex: 1 1 50%;
}
::ng-deep .cax-button.cax-button-outlined.cax-button-secondary {
    width: -webkit-fill-available;
}
::ng-deep .cax-button.cax-button-md {
    width: -webkit-fill-available;
}

.preview-tag {
    font-weight: 500;
    line-height: 20px;
    outline: none;
    white-space: nowrap;
    overflow: hidden;
    max-width: 300px;
    border: none;
    padding: 6px 8px;
    border-radius: 16px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
        opacity: 0.9;
    }
}
