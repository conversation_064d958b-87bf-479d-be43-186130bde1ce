<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en-US" datatype="plaintext" original="ng2.template" target-language="ja">
    <body>
      <trans-unit id="7309063194246193228" datatype="html">
        <source>User logged out</source>
        <target>ユーザーがログアウトしました</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_guards/auth.guard.ts</context>
          <context context-type="linenumber">54</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1505053309580789498" datatype="html">
        <source>Missing Subscription, please Retry!</source>
        <target>サブスクリプションがありません。再試行してください!</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_guards/auth.guard.ts</context>
          <context context-type="linenumber">62</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2821179408673282599" datatype="html">
        <source>Home</source>
        <target>ホーム</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">19</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7619560701830330401" datatype="html">
        <source>Loading</source>
        <target>読み込み中</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">25</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7430680760319628492" datatype="html">
        <source>Review</source>
        <target>レビュー</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">31</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">65</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6707361102856436710" datatype="html">
        <source>Products</source>
        <target>商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">37</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3342433449995226607" datatype="html">
        <source>Product Details</source>
        <target>商品詳細</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">43</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6865523355931357851" datatype="html">
        <source>Review Mode</source>
        <target>レビューモード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">49</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3807699453257291879" datatype="html">
        <source>Comments</source>
        <target>コメント</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">58</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4930506384627295710" datatype="html">
        <source>Settings</source>
        <target>設定</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">61</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4768592761478998496" datatype="html">
        <source>You are offline</source>
        <target>オフラインです</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">67</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7911416166208830577" datatype="html">
        <source>Help</source>
        <target>ヘルプ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">73</context>
        </context-group>
      </trans-unit>
      <trans-unit id="238d80a3927237a45697eff50530e92bd9958ae9" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon class=&quot;not-supported-icon&quot;&gt;"/>announcement<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/>Not Supported for Current resolution </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon class=&quot;not-supported-icon&quot;&gt;"/>お知らせ<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/>現在の解像度ではサポートされていません </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">20,22</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4063248990862762789" datatype="html">
        <source>dataX R2E: Item Data Enrichment -</source>
        <target state="translated">dataX R2: 商品データの充足 -</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.ts</context>
          <context context-type="linenumber">105</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6691324072815722351" datatype="html">
        <source>dataX R3B: Item Data Audit &amp; Enhancement -</source>
        <target state="translated">dataX R3: 商品データの監査 &amp;改善 -</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.ts</context>
          <context context-type="linenumber">109</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6508d8f187102b5b52f2c4ff41d2591e3a7a0b4c" datatype="html">
        <source>Batch Wise</source>
        <target>バッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
      </trans-unit>
      <trans-unit id="227595d92b0a4e302b5bbdf167f2e22bfbd194ce" datatype="html">
        <source>SKU Wise</source>
        <target>SKU</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ca0b86f1c8677e8091ffaab186f69c184382068e" datatype="html">
        <source>Search by Batch or SKU Id...</source>
        <target>バッチまたは SKU ID で検索...</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3d472aefa1b3cf206c479990895e30e31b0f8117" datatype="html">
        <source>Mark as Resolved</source>
        <target state="translated">解決済にする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">110,111</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d235ae9c2af04d4b113c0fad596ab23c2fe884f9" datatype="html">
        <source>Bold</source>
        <target>太字</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">235</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">275</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">365</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">328</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5d03f644fa70efc613805db7af74801cebf33bf6" datatype="html">
        <source>Italic</source>
        <target>イタリック</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">236</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">276</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">366</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">329</context>
        </context-group>
      </trans-unit>
      <trans-unit id="46bceaa64c9270b80c5b57d8a4fc96951c059539" datatype="html">
        <source>Underline</source>
        <target>下線</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">237</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">277</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">367</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">330</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b24ca8f3b3596794629fb1a49de783d5b45a275e" datatype="html">
        <source>Strikethrough</source>
        <target>取り消し線</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">238</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">368</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">331</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3ec88737802269bcef0c5cfcec11e9fe3a04e60d" datatype="html">
        <source>Quote</source>
        <target>引用</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">239</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">279</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">369</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">332</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d421ed45622751903f64590eafaa621e2ab6fa72" datatype="html">
        <source>Insert code</source>
        <target>コードを挿入</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">240</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">370</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">333</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0a5a03015269d57edcd993e5a076775f00575cad" datatype="html">
        <source>Text colour</source>
        <target>テキストの色</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">352</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">501</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">187</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">281</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">271</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">417</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">370</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">527</context>
        </context-group>
      </trans-unit>
      <trans-unit id="080a94f7efdea6749f7646f0979a70a589d431b1" datatype="html">
        <source>Heading 1</source>
        <target>見出し 1</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">244</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">374</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">337</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0d18c103cf3a2219a37129ffdba7df3cf601fde7" datatype="html">
        <source>Heading 2</source>
        <target>見出し 2</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">245</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">285</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">375</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">338</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0b970d3dfb4512896620b6666777051a71e6e99e" datatype="html">
        <source>Numbered list</source>
        <target>番号付きリスト</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">250</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">290</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">380</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">343</context>
        </context-group>
      </trans-unit>
      <trans-unit id="baa7b06bc3991ed764f12ff0f9425d2b38f3b7b7" datatype="html">
        <source>Bullet list</source>
        <target>箇条書きリスト</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">251</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">291</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">381</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">344</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9fd8aadc05058a71bad23cd93c42195de960cc3e" datatype="html">
        <source>Insert link</source>
        <target>リンクを挿入</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">260</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">300</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">390</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">353</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f8ab7d965da02f8840e06c8ee2a907bda4d3832" datatype="html">
        <source>Insert image</source>
        <target>画像を挿入</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">261</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">301</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">391</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">354</context>
        </context-group>
      </trans-unit>
      <trans-unit id="589ef1ab38f4bc265af1aac3be9dfe6d4de8920e" datatype="html">
        <source>Attach a file</source>
        <target>ファイルを添付</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">264</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">304</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">394</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">357</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e9f040d6b896bf89223b6d6fdb01ba947bd9b8ee" datatype="html">
        <source>Go fullscreen</source>
        <target>フルスクリーンにする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">273</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">403</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">366</context>
        </context-group>
      </trans-unit>
      <trans-unit id="27dbb209d5d93ba67815d781ba7d8464f8aec6e2" datatype="html">
        <source>Close fullscreen</source>
        <target>フルスクリーンを閉じる</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">404</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">367</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2d8204977190ca57f3a68f08f547330e55880c47" datatype="html">
        <source>Send</source>
        <target>送信</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">297,298</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1453051811345638430" datatype="html">
        <source>Comment or mention others with @</source>
        <target>@ を付けて他のユーザーにコメントまたはメンションする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.ts</context>
          <context context-type="linenumber">126</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.ts</context>
          <context context-type="linenumber">120</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">155</context>
        </context-group>
      </trans-unit>
      <trans-unit id="63eb45ca14797d50364b56efc165183ed6ead9c1" datatype="html">
        <source>View uploaded batches and monitor their progress</source>
        <target state="translated">バッチ単位の処理状況を確認します</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">11</context>
        </context-group>
      </trans-unit>
      <trans-unit id="92eee6be6de0b11c924e3ab27db30257159c0a7c" datatype="html">
        <source>Home</source>
        <target>ホーム</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f4331c9463fd60aab6049ba34bc1352161fa2d8d" datatype="html">
        <source>Review content at SKU level.</source>
        <target state="translated">SKU 単位の処理結果をレビューします</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6523895cfd110d53667e04c82c0f769c19d8ee74" datatype="html">
        <source>Review</source>
        <target>レビュー</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">67</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9541a91c89942b13cff2d65eb5d01796172be0d0" datatype="html">
        <source>Monitor the enrichment processes at SKU level.</source>
        <target state="translated">SKU 単位の処理状況を確認します</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
      </trans-unit>
      <trans-unit id="48dfb134cb8d324bb45f2f9f3bd7d7afc49bc3f8" datatype="html">
        <source>Products</source>
        <target>商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">101</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fe1aae48977d43d27a42764a8c62acccd108bae4" datatype="html">
        <source>Set API usage.</source>
        <target state="translated">設定を確認します</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">112</context>
        </context-group>
      </trans-unit>
      <trans-unit id="121cc5391cd2a5115bc2b3160379ee5b36cd7716" datatype="html">
        <source>Settings</source>
        <target>設定</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">134</context>
        </context-group>
      </trans-unit>
      <trans-unit id="85b79c9064aed1ead31ace985f31aa1363f6bdaf" datatype="html">
        <source>Help</source>
        <target>ヘルプ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">167</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d0227a069d21cc5c6951d829cbf135be5b5bc767" datatype="html">
        <source>Log Out</source>
        <target>ログアウト</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/top-nav/top-nav.component.html</context>
          <context context-type="linenumber">99</context>
        </context-group>
      </trans-unit>
      <trans-unit id="244aae9346da82b0922506c2d2581373a15641cc" datatype="html">
        <source>Email</source>
        <target>Eメール</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fcaecbb9243a2ba43fd7d2f212c33ec8df5f64f1" datatype="html">
        <source><EMAIL></source>
        <target><EMAIL></target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c6ab23b42faa43ed9cb09f9168566c86611c9a44" datatype="html">
        <source>Your feedback</source>
        <target>お問い合わせ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7bedf780aa4921bb525d0fc5c1468f1206a208ba" datatype="html">
        <source>Type your text here…</source>
        <target>ここにテキストを入力してください…</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">416</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">464</context>
        </context-group>
      </trans-unit>
      <trans-unit id="17a9d3860d9ad593dd09a9f934e03999d9e76a7a" datatype="html">
        <source>Cancel</source>
        <target>キャンセル</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">28,29</context>
        </context-group>
      </trans-unit>
      <trans-unit id="59804d9b69f39f46506a9bb69e8eed79c3eef8fd" datatype="html">
        <source>Save</source>
        <target>保存</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">33,34</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d2baf6889fbbd6b216109a0a822b611354bb0d1e" datatype="html">
        <source>Search by Batch Id, Name or Description</source>
        <target>バッチ ID、名前、または説明で検索</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e2b2167fc47d31dee70bfb8208daf40a1220b274" datatype="html">
        <source>Recent</source>
        <target>直近</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">20</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">42</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
      </trans-unit>
      <trans-unit id="68ff1c120cd666fdcafb27e420a58bedd20b6480" datatype="html">
        <source>Custom Range</source>
        <target>カスタム範囲</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8d40a877c935d13b6d501f3094f5c849789227d1" datatype="html">
        <source>Upload batch of SKUs to be enriched.</source>
        <target state="translated">処理するファイルをアップロードします</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">69</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a2d63bc696315b11e3010f46f0231360faf71d56" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon class=&quot;upload-btn-icon&quot;&gt;"/>add<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Upload New Batch </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon class=&quot;upload-btn-icon&quot;&gt;"/>add<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> 新しいバッチをアップロード </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">70,71</context>
        </context-group>
      </trans-unit>
      <trans-unit id="265ee68edfe57e510270da31ec99f67d94346009" datatype="html">
        <source>Reset</source>
        <target state="translated">クリア</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">74,75</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">86,87</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">88,89</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e9206a9068381f7115736176f2bded6674a54db5" datatype="html">
        <source>View comments</source>
        <target>コメントを見る</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">76</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">90</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ac0b90a4dc259e5f1f3ce6b67da8bd8334d579f8" datatype="html">
        <source>Copy text</source>
        <target>テキストをコピー</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">122</context>
        </context-group>
      </trans-unit>
      <trans-unit id="cf467767e8a4ad1145432712daa5728afbf39ae7" datatype="html">
        <source>Download input file</source>
        <target>入力データのダウンロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">141</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d7b35c384aecd25a516200d6921836374613dfe7" datatype="html">
        <source>Cancel</source>
        <target>キャンセル</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">226</context>
        </context-group>
      </trans-unit>
      <trans-unit id="826b25211922a1b46436589233cb6f1a163d89b7" datatype="html">
        <source>Delete</source>
        <target state="translated">削除</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">230</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">237</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e972e3f5ec910d9d1513545389dd0fcda77d0947" datatype="html">
        <source>Approve the batch</source>
        <target>バッチを承認する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">246</context>
        </context-group>
      </trans-unit>
      <trans-unit id="bce22c8762862737d9878a3027ae397723aeb315" datatype="html">
        <source>Download enriched data</source>
        <target state="translated">アウトプットデータをダウンロードする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">280</context>
        </context-group>
      </trans-unit>
      <trans-unit id="339ba1401f1dba2ab7bf8733f58b5cd49cef1eec" datatype="html">
        <source>Upload New Batch</source>
        <target>新しいバッチをアップロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">362</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7437f105cc63417f5811823d85c809e9f90caf90" datatype="html">
        <source>folder_open</source>
        <target>folder_open</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">371</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a538bf774805898f018a89c144b373a23550a370" datatype="html">
        <source>Upload the data similar to the format specified in the <x id="START_LINK" ctype="x-a" equiv-text="&lt;a class=&quot;sample-docs text-theme-primary&quot;&gt;"/><x id="START_TAG_NG_CONTAINER" ctype="x-ng_container" equiv-text="&lt;ng-container&gt;"/>Sample Document<x id="CLOSE_TAG_NG_CONTAINER" ctype="x-ng_container" equiv-text="&lt;/ng-container&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/><x id="TAG_IMG" ctype="image" equiv-text="&lt;img class=&quot;download-sample-doc&quot; src=&quot;../../../assets/images/home-page/download.svg&quot; class=&quot;svg-icon&quot; /&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/></source>
        <target><x id="START_LINK" ctype="x-a" equiv-text="&lt;a class=&quot;sample-docs text-theme-primary&quot;&gt;"/><x id="START_TAG_NG_CONTAINER" ctype="x-ng_container" equiv-text="&lt;ng-container&gt;"/>サンプルドキュメント<x id="CLOSE_TAG_NG_CONTAINER" ctype="x-ng_container" equiv-text="&lt;/ng-container&gt;"/><x id="START_TAG_SPAN" ctype="x-span" equiv-text="&lt;span&gt;"/><x id="TAG_IMG" ctype="image" equiv-text="&lt;img class=&quot;download-sample-doc&quot; src=&quot;../../../assets/images/home-page/download.svg&quot; class=&quot;svg-icon&quot; /&gt;"/><x id="CLOSE_TAG_SPAN" ctype="x-span" equiv-text="&lt;/span&gt;"/><x id="CLOSE_LINK" ctype="x-a" equiv-text="&lt;/a&gt;"/>で指定された形式と同様のデータをアップロードします</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">381,387</context>
        </context-group>
      </trans-unit>
      <trans-unit id="978fb083459fe36247ddc5814f4e075403483ffd" datatype="html">
        <source>Batch Name</source>
        <target>バッチ名</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">396</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e0ebf17363e9df7dcb39da79e026a10141f8c965" datatype="html">
        <source>Eg. Batch_104.csv</source>
        <target state="translated">例) バッチ_104.csv</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">398</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f86f859d52c766a9a3c48b648c8a291b726c78b" datatype="html">
        <source>Input Format*</source>
        <target>入力フォーマット*</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">401</context>
        </context-group>
      </trans-unit>
      <trans-unit id="564eefde47882585f94b906f3bf20c1ffecb85cb" datatype="html">
        <source>Output Format*</source>
        <target>出力フォーマット*</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">408</context>
        </context-group>
      </trans-unit>
      <trans-unit id="eec715de352a6b114713b30b640d319fa78207a0" datatype="html">
        <source>Description</source>
        <target>説明</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">414</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">462</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7529307ff5a8475ecd0755abb1b8a91ae24c1d52" datatype="html">
        <source>Reference</source>
        <target>参照</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">418</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7d178bcf6e2595b25eeb1463ca9444140efd7e1c" datatype="html">
        <source>Upload</source>
        <target>アップロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">436,437</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d9a6d2f30e57a02a6b36a7773c39a3791c42ae9c" datatype="html">
        <source>Add Tag</source>
        <target>タグ付けする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">444</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e1b8c658165692659d6ad0a7eb28f782cb011dda" datatype="html">
        <source>Create New Tag</source>
        <target>新しいタグを作成</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">458</context>
        </context-group>
      </trans-unit>
      <trans-unit id="dfb8f871e1dbe50025b54267a4466e2a480801fc" datatype="html">
        <source>Type your text here...</source>
        <target state="translated">ここにテキストを入力してください...</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">460</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8855d0ad02000ba43dda7f8c164c1c8c0715bdb5" datatype="html">
        <source>Tag Color</source>
        <target>タグの色</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">474</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3cf1feec60b322423082c3d20d84823d17331da3" datatype="html">
        <source>Create Tag</source>
        <target>タグを作成</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">489,490</context>
        </context-group>
      </trans-unit>
      <trans-unit id="db09b9d7f658ad13a3e90b2af2836711c97965e3" datatype="html">
        <source>Batch Log</source>
        <target>バッチログ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">496</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3537625483230066456" datatype="html">
        <source>Batch ID</source>
        <target>バッチID</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">49</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="4947493162669190104" datatype="html">
        <source>Name &amp; Description</source>
        <target state="translated">バッチ名/説明</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">50</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7886570921510760899" datatype="html">
        <source>Tags</source>
        <target>タグ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">51</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="4948663886508626048" datatype="html">
        <source>Created On</source>
        <target>作成日</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">52</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3313063645488579833" datatype="html">
        <source>ETA</source>
        <target>完了予定時間</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">53</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="8837798131261467122" datatype="html">
        <source>Total Rows</source>
        <target state="translated">件数</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">54</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7317290694079257887" datatype="html">
        <source>Accepted</source>
        <target>受入可</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">55</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">59</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7040288612202036661" datatype="html">
        <source>Others</source>
        <target>その他</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">56</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3193976279273491157" datatype="html">
        <source>Actions</source>
        <target>アクション</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">57</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3966216282658417930" datatype="html">
        <source>In Queue</source>
        <target>待機中</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">111</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="2914293374339727314" datatype="html">
        <source>Batches that are undergoing validation prior to processing</source>
        <target state="translated">待機中のバッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">113</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="1780950485960518731" datatype="html">
        <source>In Progress</source>
        <target>処理中</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">116</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">53</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="6932646515804747139" datatype="html">
        <source>Batches that are undergoing attribute enrichment</source>
        <target state="translated">処理中のバッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">118</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="4749295647449765550" datatype="html">
        <source>Processed</source>
        <target>処理済</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">121</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="8630102797980407100" datatype="html">
        <source>Batches that have completed the enrichment process. Possible actions: Approve the batch / Download enriched data / Download input data</source>
        <target state="translated">処理済のバッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">123</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="5906272961955453157" datatype="html">
        <source>Approved</source>
        <target>承認済</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">126</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="2160057240753842105" datatype="html">
        <source>Batches that have been approved post-enrichment. Possible actions: Download enriched data / Download input file</source>
        <target state="translated">承認済のバッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">128</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="4043049091728011235" datatype="html">
        <source>Cancelled</source>
        <target state="translated">キャンセル済</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">131</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="3260816546187784699" datatype="html">
        <source>Batches that could not be processed because of invalid data</source>
        <target state="translated">キャンセル済のバッチ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">133</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="5527182815413582066" datatype="html">
        <source>Modify ETA</source>
        <target>完了予定時間の変更</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">139</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8111481560273086490" datatype="html">
        <source>Add ETA</source>
        <target>完了予定時間を追加</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">140</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1676946925441352884" datatype="html">
        <source>Last Week</source>
        <target>先週</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">204</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">270</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">127</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">249</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5301451406229114785" datatype="html">
        <source>Last Month</source>
        <target>先月</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">209</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">275</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">132</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">254</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8065968369037299258" datatype="html">
        <source>Last Quarter</source>
        <target>前四半期</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">217</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">283</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">140</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">262</context>
        </context-group>
      </trans-unit>
      <trans-unit id="771f5b8ed5db67f8a835828a038be46b4b57850e" datatype="html">
        <source>Access Denied!!</source>
        <target>アクセスが拒否されました！！</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/loading/loading.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7a7641450828f8c58f57dc5a366ecb606ac1e130" datatype="html">
        <source>The page you are trying to reach cannot be accessed at the moment.</source>
        <target>アクセスしようとしているページには、現在アクセスできません。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/loading/loading.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3618ef2afb7155cd662abdd58c4929919f907b25" datatype="html">
        <source>Update</source>
        <target>アップデート</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">24,25</context>
        </context-group>
      </trans-unit>
      <trans-unit id="acb391ab758c48309619815ee1452bf2433903eb" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon matPrefix&gt;"/>west<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Prev </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon matPrefix&gt;"/>西<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> 前のページ </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">27,29</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">78,80</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d95703cb6547e5a00d8c11cc2bd6efc4218a4244" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon matPrefix&gt;"/>east<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Next </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon matPrefix&gt;"/>東<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/>次 </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">31,33</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fba0699b3bac7753b497fa55765ace9d7b893883" datatype="html">
        <source>Product Details</source>
        <target>商品詳細</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">41,42</context>
        </context-group>
      </trans-unit>
      <trans-unit id="51f9926620b9cc6b5476300c67e086754183812d" datatype="html">
        <source>Row ID :</source>
        <target state="translated">SKU ID :</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">64</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ca0cb1f4418e3a70200a80a5a829a08a3a28760a" datatype="html">
        <source>Batch ID :</source>
        <target>バッチ ID :</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">66</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5e8a4dceb0a2c50245884c3298ab95ad051f6c53" datatype="html">
        <source>Move To</source>
        <target>移動する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">82</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">245</context>
        </context-group>
      </trans-unit>
      <trans-unit id="083338c1e13796fd0259d89493ec7b75be0d14d0" datatype="html">
        <source>Read only</source>
        <target>読み取り専用</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">121</context>
        </context-group>
      </trans-unit>
      <trans-unit id="01120284526728d672843d2149895c9b2364c71b" datatype="html">
        <source>No Data found</source>
        <target>何もデータが見つかりませんでした</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">157,158</context>
        </context-group>
      </trans-unit>
      <trans-unit id="353137c89f9e0720cc6f9590d81cd53df9c13a6a" datatype="html">
        <source>Send</source>
        <target>送信</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/product-details.component.html</context>
          <context context-type="linenumber">330,331</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d900c9c42e6f296a49a46079b5075765bfd04442" datatype="html">
        <source>Search MPN, Batch ID, Row ID</source>
        <target state="translated">品番、バッチ ID、SKU ID を検索</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8946b0053f321c7db24853d1b213b1ea9794c789" datatype="html">
        <source>Custom Range</source>
        <target>カスタム範囲</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">72</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c2375280c2228be7eb5fc89446188193eb20f03b" datatype="html">
        <source>Prev</source>
        <target>前へ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f732c304c7433e5a83ffcd862c3dce709a0f4982" datatype="html">
        <source>Next</source>
        <target>次</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">81</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c5bb9f8936cb33db2ca351a1b87a984fdf11917c" datatype="html">
        <source>Comments</source>
        <target>コメント</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">91</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">127</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">117</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f6714615c2540e04519058d911785ab23d66b44b" datatype="html">
        <source>Product Details</source>
        <target>商品詳細</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">102,103</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a8026c4fe7ec0999f852e71393dca77c9e3b6f77" datatype="html">
        <source>Product Details</source>
        <target>商品詳細</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">125</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">115</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8e45f9d35b6ab25da0cf068dd0f415ccef9fec9b" datatype="html">
        <source>Suggestions &amp; Edits</source>
        <target state="translated">提案 &amp;編集</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">126</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">116</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0dc2e6fb6d0244d5024e73ede409a45d5b0c8a64" datatype="html">
        <source>References</source>
        <target>参考文献</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">181</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">172</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2955e9d34ed96a4b937abac9bee3aea9c36c3050" datatype="html">
        <source>Request Rework</source>
        <target>再処理を依頼する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">221,222</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">212,213</context>
        </context-group>
      </trans-unit>
      <trans-unit id="132c5be5b08c847b47a2fc9ab92118a9da95bdaf" datatype="html">
        <source>Request Review</source>
        <target>レビューをリクエスト</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">225,226</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1535211875b523c59bc47ba0b78d3004d9bef8c5" datatype="html">
        <source>Accept All Suggestions</source>
        <target>すべての提案を受け入れる</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">230,231</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">217,218</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7ff1317eee6f83d2d803aff3cfecd41fb62461a7" datatype="html">
        <source>No Suggestions available</source>
        <target state="translated">提案はありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">240</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">227</context>
        </context-group>
      </trans-unit>
      <trans-unit id="84dc7b86c3c4993d5d3e7204503c85ce96852e3e" datatype="html">
        <source>ATTRIBUTES</source>
        <target>属性</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">253,254</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">240,241</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e19737ed5663744451a005ad1dee2c8e218a4e26" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{ item.predictions.length - defSuggestionDisplayCount }}"/> More Suggestions <x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon&gt;"/>arrow_drop_down<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/></source>
        <target><x id="INTERPOLATION" equiv-text="{{ item.predictions.length - defSuggestionDisplayCount }}"/> 項目の提案 <x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon&gt;"/>arrow_drop_down<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/></target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">320,323</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">307,310</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b4805615a46102cda424b0f8ccb4ad6a7d90ff6b" datatype="html">
        <source>Send</source>
        <target>送信</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">426,427</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b5b67ff97e9bcbac7c5c30a145ffc05845d105fa" datatype="html">
        <source>Nothing to display</source>
        <target state="translated">商品がありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/product-details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">523</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">286</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">508</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1941c2ecd2dbae945e0a0847ff43be0a5806d9c3" datatype="html">
        <source>Search SKU, Product Name, Batch ID</source>
        <target>SKU、商品名、バッチ ID を検索</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4e5a20fd0c65bdc6830b2205b38fca3a6362d518" datatype="html">
        <source>Clear search and filters</source>
        <target>検索とフィルタをクリア</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">153</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c741e919cae3e4e5d7a6e43da8926bb99b1fd780" datatype="html">
        <source>Reset</source>
        <target state="translated">クリア</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">157,158</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1655609962937702330" datatype="html">
        <source>All SKUs that are undergoing enrichment</source>
        <target state="translated">処理中の商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">56</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="2355241115004164604" datatype="html">
        <source>All SKUs enriched by dataX with high confidence and don't need to be reviewed. You can still choose to make minor edits and add comments here. Possible actions: Leave as 'Accepted' / Move to 'Review' / Move to 'Rework'</source>
        <target state="translated">受入可の商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">62</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="3955632182222112807" datatype="html">
        <source>SKUs that have undergone enrichment, but dataX has less confidence in, and requires your review. You can make minor edits and add comments here. Possible actions: Move to 'Accepted' / Move to 'Rework'</source>
        <target state="translated">レビューが必要な 商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">68</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="6583773138777204909" datatype="html">
        <source>Rework</source>
        <target>再処理</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">71</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="8656057706886146282" datatype="html">
        <source>SKUs that need to be enriched again. Post-enrichment, the SKUs appear in the 'Accepted' bucket</source>
        <target state="translated">再処理が必要な商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">74</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="908856157228300595" datatype="html">
        <source>Insufficient Data</source>
        <target>データ不十分</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="3790712685535029653" datatype="html">
        <source>SKUs that cannot be enriched because of missing data</source>
        <target state="translated">データ不十分のため処理できない 商品</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">80</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="2251dc3bea53473187b66237dd814f4146f59fd9" datatype="html">
        <source>Next <x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon&gt;"/>east<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/></source>
        <target>次の<x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon&gt;"/>東<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/></target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">82,83</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f909d1da7fe5caf97324309df6b6af43f72809df" datatype="html">
        <source>View More</source>
        <target>もっと見る</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">187</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a17212637b13dfba2bfdab92623f5c467ba906e4" datatype="html">
        <source>Perform the enhancement again</source>
        <target>再度充足する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">211</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1948e0cfb6d121694c8e30adbea30b42a0e2902d" datatype="html">
        <source>Mark the item 'Accepted'</source>
        <target>商品を「受入可」とマークする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">215</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5e15457d8c707fe6483a83ec1eec77f6fac95754" datatype="html">
        <source>Existing</source>
        <target state="translated">現在</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">252</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1fddd2204c0c24c6780510066ad45e28391581dc" datatype="html">
        <source>Suggestion</source>
        <target>提案</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">259</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8fce27c05e8133a86863a4a5e9135d55898bb114" datatype="html">
        <source>Send</source>
        <target>送信</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">389,390</context>
        </context-group>
      </trans-unit>
      <trans-unit id="69fe62bc77a09c1e59c33c73535fa737cb87cb11" datatype="html">
        <source>API Usage</source>
        <target state="translated">API の利用状況</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5ca707824ab93066c7d9b44e1b8bf216725c2c22" datatype="html">
        <source>Filter</source>
        <target>フィルター</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6762743264882388498" datatype="html">
        <source>Monthly</source>
        <target>月</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.ts</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="928838138686087140" datatype="html">
        <source>Weekly</source>
        <target>週</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.ts</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit>
      <trans-unit id="707147927148181991" datatype="html">
        <source>Items per page:</source>
        <target>ページあたりの表示数:</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">86</context>
        </context-group>
      </trans-unit>
      <trans-unit id="797234997496878479" datatype="html">
        <source>Next page</source>
        <target>次ページ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">87</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5703462352676845579" datatype="html">
        <source>Previous page</source>
        <target>前ページ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">88</context>
        </context-group>
      </trans-unit>
      <trans-unit id="490d7184d49f94f736f76f1b2cbbaeb9012bad9e" datatype="html">
        <source>No Comments yet.</source>
        <target>コメントはまだありません。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">118</context>
        </context-group>
      </trans-unit>
      <trans-unit id="feca47f1da7c9cf5c241dacfcc4709c3fc50ef84" datatype="html">
        <source>To create a new comment, use @username</source>
        <target>新しいコメントを作成するには、@username を使用します</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/comments/comments.component.html</context>
          <context context-type="linenumber">119</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b86f00992e544288c25690150bee4204a9088194" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon fxLayoutAlign=&quot;space-between center&quot;&gt;"/>folder_open<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Drag or Drop your files here </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon fxLayoutAlign=&quot;space-between center&quot;&gt;"/>folder_open<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> ここにファイルをドラッグまたはドロップします </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">371,373</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3484994613114698934" datatype="html">
        <source>Upload Complete</source>
        <target>アップロード完了</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">587</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2332375809702738744" datatype="html">
        <source>Missing Subscription, Redirecting ...</source>
        <target>サブスクリプションがありません。リダイレクトしています...</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_guards/auth.guard.ts</context>
          <context context-type="linenumber">65</context>
        </context-group>
      </trans-unit>
      <trans-unit id="168924cff252eeadfacbceff4e1039aa3c3dad69" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon fxLayoutAlign=&quot;space-between center&quot;&gt;"/>folder_open<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Drag and Drop your files here </source>
        <target><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon fxLayoutAlign=&quot;space-between center&quot;&gt;"/>folder_open<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> ここにファイルをドラッグ アンド ドロップします </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">371,373</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2825093609304249365" datatype="html">
        <source>Uploaded</source>
        <target>アップロード済み</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">533</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5303674099842475535" datatype="html">
        <source>Drag and Drop your files here</source>
        <target>ここにファイルをドラッグ アンド ドロップします</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">534</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6932865105766151309" datatype="html">
        <source>Upload</source>
        <target>アップロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">535</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6dc7fa16d9103d24f919e62bb41ca1598e244933" datatype="html">
        <source>Approve</source>
        <target>承認</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">248,249</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7568168175661014733" datatype="html">
        <source>View More</source>
        <target>表示を増やす</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">630</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">695</context>
        </context-group>
      </trans-unit>
      <trans-unit id="642420481690975524" datatype="html">
        <source>View less</source>
        <target>表示を減らす</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">634</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">699</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5321159160191726759" datatype="html">
        <source>Please use Next Button to view more products</source>
        <target>次のボタンを使用して、他の商品を表示してください</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">357</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1235</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8642445555713453888" datatype="html">
        <source>First page</source>
        <target>先頭ページ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">88</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6000244294113297981" datatype="html">
        <source>Last page</source>
        <target>最後のページ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">89</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d190b337376ad098c70ad57691be1d404e813720" datatype="html">
        <source>Download <x id="TAG_IMG" ctype="image" equiv-text="&lt;img [src]=&quot;                             t.menuOpen                               ? '../../assets/images/landing-page/up-arrow-white.svg'                               : '../../assets/images/landing-page/down-arrow-white.svg'                           &quot; /&gt;"/></source>
        <target><x id="TAG_IMG" ctype="image" equiv-text="&lt;img [src]=&quot;                             t.menuOpen                               ? '../../assets/images/landing-page/up-arrow-white.svg'                               : '../../assets/images/landing-page/down-arrow-white.svg'                           &quot; /&gt;"/> をダウンロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">282,287</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5396470884744387564" datatype="html">
        <source>Download output file</source>
        <target>出力ファイルをダウンロード</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">106</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7934833136974560675" datatype="html">
        <source>Retry</source>
        <target>リトライ</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">592</context>
        </context-group>
      </trans-unit>
      <trans-unit id="95712245417000825" datatype="html">
        <source>Couldn't resume upload, Please try again!</source>
        <target>アップロードを再開できませんでした。もう一度お試しください。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">593</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5c554a2d805b39c3199f539aa34bd843a9ad3023" datatype="html">
        <source>Output File</source>
        <target>出力ファイル</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">301</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ee8f8008bae6ce3a49840c4e1d39b4af23d4c263" datatype="html">
        <source>Assets</source>
        <target state="translated">デジタルアセット</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">305</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0bf21e696bd2232ad574df23453e9e3129d9946d" datatype="html">
        <source>No batch in queue.</source>
        <target state="translated">待機中のバッチはありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">335,336</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f3eedbdf565df433bb9f992d004ab49b3be8766" datatype="html">
        <source>No batch in progress.</source>
        <target state="translated">処理中のバッチはありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">337,338</context>
        </context-group>
      </trans-unit>
      <trans-unit id="50a12600c3d7d7894123cc2cc44140916ca29df5" datatype="html">
        <source>Processing has not started.</source>
        <target state="translated">処理済のバッチはありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">339,340</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b9c9e0d3caf491ec3be922fb679e7608b7a9a545" datatype="html">
        <source>No batch has been approved yet.</source>
        <target state="translated">承認済のバッチはありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">341,342</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9691c2427ae565ef8cf6591b8e559339322ed93c" datatype="html">
        <source>No batch has been cancelled.</source>
        <target state="translated">キャンセルされたバッチはありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">343,344</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7460287376550328285" datatype="html">
        <source>PROCESSING</source>
        <target>処理</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/undo-snackbar/undo-snackbar.component.ts</context>
          <context context-type="linenumber">38</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b6bed96af13bc125110fc3f5f51717df732221c1" datatype="html">
        <source>Drag and Drop your files here</source>
        <target>ここにファイルをドラッグ アンド ドロップします</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">372</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7019108526554724390" datatype="html">
        <source>Processing... please wait</source>
        <target>処理中...お待ちください</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">1098</context>
        </context-group>
      </trans-unit>
      <trans-unit id="680d5c75b7fd8d37961083608b9fcdc4167b4c43" datatype="html">
        <source>Previous</source>
        <target>前</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
      </trans-unit>
      <trans-unit id="28f86ffd419b869711aa13f5e5ff54be6d70731c" datatype="html">
        <source>Edit</source>
        <target>編集</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">158</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d7169ace1e543bb2776a08b98e57e20de49b814c" datatype="html">
        <source>Cancel</source>
        <target>キャンセル</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">238,239</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d32a734b23805a90ed59be205fb10dcf11b347c1" datatype="html">
        <source>Update</source>
        <target>アップデート</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">244,245</context>
        </context-group>
      </trans-unit>
      <trans-unit id="986713bd00465ca6c60f68c344b6c8664e32cbe2" datatype="html">
        <source>No comments to display.</source>
        <target>表示するコメントはありません。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">333</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5392341774767336507" datatype="html">
        <source>Copied!</source>
        <target>コピーしました！</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">479</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d460ef21f0de65e35a799269e80fd58a85c199e7" datatype="html">
        <source>Actions</source>
        <target>アクション</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">215,216</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5530715537995053237" datatype="html">
        <source>You have viewed all the products in this batch</source>
        <target>このバッチのすべての商品を表示しました</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1276</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8998179362936748717" datatype="html">
        <source>OK</source>
        <target>OK</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1277</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2bdf445d7c72ce1d0011f636be7010b7b2c1db3c" datatype="html">
        <source>Are you sure ?</source>
        <target>本当にそうですか ？</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-delete/confirm-delete.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5d49ee389add9351d1a4e56d4c8200eff140b932" datatype="html">
        <source>Close</source>
        <target>近い</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-delete/confirm-delete.component.html</context>
          <context context-type="linenumber">6,7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d9797b7f7d130802253f3bdd06f7da9f474816af" datatype="html">
        <source>Delete</source>
        <target>消去</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-delete/confirm-delete.component.html</context>
          <context context-type="linenumber">9,10</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6546893268910523216" datatype="html">
        <source>No more Products Available</source>
        <target>これ以上利用可能な商品はありません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1200</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d095084d8121f7d563d02146a33b797536b1ceab" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{secondaryBtn}}"/> </source>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{secondaryBtn}}"/> </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-dialog/confirm-dialog.component.html</context>
          <context context-type="linenumber">6,8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="177d8a296eda59a30ab4f1cc813685b2627b2629" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{primaryBtn | titlecase}}"/> </source>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{secondaryBtn}}"/> </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-dialog/confirm-dialog.component.html</context>
          <context context-type="linenumber">9,11</context>
        </context-group>
      </trans-unit>
      <trans-unit id="236c7c866649d0fad4b5e6d286da8aa7d8666b6d" datatype="html">
        <source>UNDO</source>
        <target state="translated">元に戻す</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/undo-snackbar/undo-snackbar.component.html</context>
          <context context-type="linenumber">7,8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0e94a9e11289a09896d406059ec243204e328fad" datatype="html">
        <source>Batch Name*</source>
        <target state="translated">バッチ名*</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">396</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5889356921455381363" datatype="html">
        <source>Are you sure you want to approve <x id="PH" equiv-text="batch_id"/>? No further processing is possible after the batch is approved.</source>
        <target state="translated"><x id="PH" equiv-text="batch_id"/> を承認してもよろしいですか？バッチが承認された後は、それ以上の修正はできなくなります</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">898,899</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5946223627892934995" datatype="html">
        <source>Are you sure you want to cancel <x id="PH" equiv-text="batch_id"/>? This will move the batch to 'Cancelled' tab.</source>
        <target state="translated"><x id="PH" equiv-text="batch_id"/> 本当にキャンセルしますか？ 「キャンセル済」 タブに移動します</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">901,902</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4193058338493003653" datatype="html">
        <source>Are you sure you want to delete <x id="PH" equiv-text="batch_id"/>? This will delete the batch from Cancelled.</source>
        <target state="translated"><x id="PH" equiv-text="batch_id"/> を削除してもよろしいですか? これにより、キャンセル済からバッチが削除されます</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">904,905</context>
        </context-group>
      </trans-unit>
      <trans-unit id="381510821082421688" datatype="html">
        <source>Are you sure you want to delete <x id="PH" equiv-text="batch_id"/>?</source>
        <target state="translated"><x id="PH" equiv-text="batch_id"/> を削除してもよろしいですか?</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">906</context>
        </context-group>
      </trans-unit>
      <trans-unit id="047f50bc5b5d17b5bec0196355953e1a5c590ddb" datatype="html">
        <source>Update</source>
        <target state="translated">アップデート</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">566</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">490</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">593</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4813239392166088334" datatype="html">
        <source>Are you sure you want to delete this comment?</source>
        <target state="translated">このコメントを削除してもよろしいですか?</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1013</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">875</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">843</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1063</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7022070615528435141" datatype="html">
        <source>Delete</source>
        <target state="translated">削除</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1015</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">873</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">845</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1065</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7819314041543176992" datatype="html">
        <source>Close</source>
        <target state="translated">閉じる</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1016</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">874</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">846</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1066</context>
        </context-group>
      </trans-unit>
      <trans-unit id="baab794a832629aeb31830a38fbc4bd876a89645" datatype="html">
        <source>Request Rework</source>
        <target state="translated">やり直しを依頼する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">223</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ad895501c63c9edbd8ec03998cafad67f4d5f471" datatype="html">
        <source>Request Review</source>
        <target state="translated">レビューをリクエスト</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">229</context>
        </context-group>
      </trans-unit>
      <trans-unit id="711291cc25ecb4d39ae2b2917e6919f2a3ad5fc7" datatype="html">
        <source>Accept All Suggestions</source>
        <target state="translated">すべての提案を受け入れる</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">237</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">237</context>
        </context-group>
      </trans-unit>
      <trans-unit id="560328960231561562" datatype="html">
        <source>Error uploading file, Retry!</source>
        <target state="translated">ファイルのアップロード中にエラーが発生しました。再試行してください。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">598</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">658</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4082960634819166485" datatype="html">
        <source>APPROVE</source>
        <target state="translated">承認</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">937</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6943486270557756671" datatype="html">
        <source>CANCEL</source>
        <target state="translated">キャンセル</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">939</context>
        </context-group>
      </trans-unit>
      <trans-unit id="841311519047609428" datatype="html">
        <source>DELETE</source>
        <target state="translated">削除</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">940</context>
        </context-group>
      </trans-unit>
      <trans-unit id="99c42f8f451e4cd204d9affebcec0cd439a13778" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{uploadButtonLabel}}"/> </source>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{secondaryBtn}}"/> </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">425,427</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5388379548328685884" datatype="html">
        <source>Uploading..</source>
        <target state="translated">アップロード中..</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">518</context>
        </context-group>
      </trans-unit>
      <trans-unit id="62119c2e6fbb29cb0350ebda501ca23ad6d330c7" datatype="html">
        <source>Not Supported for Current resolution</source>
        <target state="translated">現在の解像度ではサポートされていません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">25,26</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4fdd510f100358a2afa2dc81aa1a64fc6dd6f042" datatype="html">
        <source>Not Supported for Current resolution</source>
        <target state="translated">現在の解像度ではサポートされていません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1c1c277ec3b4c7b91026d05bd5d272c6a632b3ec" datatype="html">
        <source>View More</source>
        <target state="translated">もっと見る</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">188</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7548030408048905388" datatype="html">
        <source>An unexpected error has occurred. Please contact dataX support.</source>
        <target state="translated">予期しないエラーが発生しました。 dataX サポートにお問い合わせください。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/services/snackbar.service.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7289632971788786960" datatype="html">
        <source>File can't be empty</source>
        <target state="translated">ファイルを空にすることはできません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">515</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1034826c0b84eaa77de6438fa5ab414e0331857b" datatype="html">
        <source>Batch name is required</source>
        <target state="translated">バッチ名が必要です</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">398,399</context>
        </context-group>
      </trans-unit>
      <trans-unit id="567797f7f469975d301af720cc2c4cc71e0be940" datatype="html">
        <source>Reference should not exceed 15 characters</source>
        <target state="translated">参照は 15 文字を超えてはなりません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">431,432</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b640769d91bfeee0a75ba1ddfbde1e64880040c3" datatype="html">
        <source>Close</source>
        <target state="translated">閉じる</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">16,17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="92c952c40beaef3b4dac2c411ee606731046d2c8" datatype="html">
        <source>View Details</source>
        <target state="translated">詳細を見る</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">19,20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ebd5b96fb776c97bd6986bacf5a857dbb2e4862c" datatype="html">
        <source>Approve Anyway</source>
        <target state="translated">承認する</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">22,23</context>
        </context-group>
      </trans-unit>
      <trans-unit id="613f7ad7623a7e288929ce093480d6be05fab94d" datatype="html">
        <source>Batch name should not exceed 100 characters</source>
        <target state="translated">バッチ名は 100 文字を超えてはなりません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">401,402</context>
        </context-group>
      </trans-unit>
      <trans-unit id="cbec952a0ff4faf2f56f0e6014c391ae87aedb34" datatype="html">
        <source>Description should not exceed 2000 characters</source>
        <target state="translated">説明は 2000 文字を超えないようにしてください</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">424,425</context>
        </context-group>
      </trans-unit>
      <trans-unit id="97e7da30de7499fcf212e1e0fd717865ce2aca3d" datatype="html">
        <source>View Details</source>
        <target state="translated">詳細を見る</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">223</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">216</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9521ac2f7b4f9cb042b8c6be586ca7d7dfb5954b" datatype="html">
        <source>Approve Batch</source>
        <target state="translated">バッチの承認</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6dd256d045cfa0d2dc7764f8f156d52d3afcec2d" datatype="html">
        <source>Out of <x id="INTERPOLATION" equiv-text="{{batchData['total_rows']}}"/> SKUs in Batch <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/><x id="INTERPOLATION_1" equiv-text="{{batchId}}"/><x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/>, there are: </source>
        <target state="translated">バッチ <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/><x id="INTERPOLATION_1" equiv-text="{{batchId}}"/><x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/> に含まれる <x id="INTERPOLATION" equiv-text="{{batchData['total_rows']}}"/> 件のステータスは、次のとおりです </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="887f21a922708c5148831f4003876f86229df521" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData['accepted']}}"/> in 'Accepted'</source>
        <target state="translated">受入可: <x id="INTERPOLATION" equiv-text="{{batchData['accepted']}}"/> 件</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
      <trans-unit id="69463d331185b2f9df0dfafbea417b8e0971d69c" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData['review']}}"/> in 'Review'</source>
        <target state="translated">レビュー: <x id="INTERPOLATION" equiv-text="{{batchData['review']}}"/> 件</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c4540de3af56d10c417f6d27fb3e811e5d9ef334" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData['rework']}}"/> in 'Rework'</source>
        <target state="translated">再処理: <x id="INTERPOLATION" equiv-text="{{batchData['rework']}}"/> 件</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f9d1942e9728a4c3c098447ce10f756f14c36a81" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData['insufficient_data']}}"/> in 'Insufficient Data'</source>
        <target state="translated">データ不十分: <x id="INTERPOLATION" equiv-text="{{batchData['insufficient_data']}}"/> 件</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="66e706f03a85c2cdadf2cd7987253e9a34938957" datatype="html">
        <source>Are you sure you want to approve the entire batch? No further processing of SKUs is possible after the batch is marked 'Approved'.</source>
        <target state="translated">バッチを承認してもよろしいですか? バッチが承認された後は、そのバッチの商品 は修正できなくなります</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">12,13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5937251202465808296" datatype="html">
        <source>More</source>
        <target state="translated">もっと</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">75</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="8899155811401566770" datatype="html">
        <source>Download insufficient data SKUs</source>
        <target state="translated">不十分なデータ SKU をダウンロードする</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">122</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5015562350739866629" datatype="html">
        <source>File generation in progress. Please wait.</source>
        <target state="translated">ファイル生成中です。お待ちください。</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">123</context>
        </context-group>
      </trans-unit>
      <trans-unit id="377cfe4ba52c469fd7080707c122440411d86f20" datatype="html">
        <source>Insufficient Data SKUs</source>
        <target state="translated">不十分なデータ SKU</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">334</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5b2b053aab96de9fe69ae0b94cecf02228d90427" datatype="html">
        <source>Copy reference</source>
        <target state="translated">参照のコピー</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">370</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b9a4505bbf4041d75de176023fd16cee23633834" datatype="html">
        <source>Reference should not exceed 1000 characters</source>
        <target state="translated">参照は 1000 文字を超えてはなりません</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">514,515</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6bb8c7f06a0a1b50aa1c9a09012fa66350d4cdef" datatype="html">
        <source>Maximum of 10 References can be added</source>
        <target state="translated">最大 10 件の参照を追加できます</target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">531,532</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4cf935702f186c4cd692d2076a82feb47572f411" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{ uploadButtonLabel }}"/> </source>
        <target state="translated"><x id="INTERPOLATION" equiv-text="{{ uploadButtonLabel }}"/> </target>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">540,542</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
