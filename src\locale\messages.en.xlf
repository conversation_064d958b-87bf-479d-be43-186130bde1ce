<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en-US" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="9521ac2f7b4f9cb042b8c6be586ca7d7dfb5954b" datatype="html">
        <source>Approve Batch</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">2</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6dd256d045cfa0d2dc7764f8f156d52d3afcec2d" datatype="html">
        <source>Out of <x id="INTERPOLATION" equiv-text="{{batchData[&apos;total_rows&apos;]}}"/> SKUs in Batch <x id="START_BOLD_TEXT" ctype="x-b" equiv-text="&lt;b&gt;"/><x id="INTERPOLATION_1" equiv-text="{{batchId}}"/><x id="CLOSE_BOLD_TEXT" ctype="x-b" equiv-text="&lt;/b&gt;"/>, there are: </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="887f21a922708c5148831f4003876f86229df521" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData[&apos;accepted&apos;]}}"/> in &apos;Accepted&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
      <trans-unit id="69463d331185b2f9df0dfafbea417b8e0971d69c" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData[&apos;review&apos;]}}"/> in &apos;Review&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c4540de3af56d10c417f6d27fb3e811e5d9ef334" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData[&apos;rework&apos;]}}"/> in &apos;Rework&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f9d1942e9728a4c3c098447ce10f756f14c36a81" datatype="html">
        <source><x id="INTERPOLATION" equiv-text="{{batchData[&apos;insufficient_data&apos;]}}"/> in &apos;Insufficient Data&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="66e706f03a85c2cdadf2cd7987253e9a34938957" datatype="html">
        <source> Are you sure you want to approve the entire batch? No further processing of SKUs is possible after the batch is marked &apos;Approved&apos;. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">12,13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b640769d91bfeee0a75ba1ddfbde1e64880040c3" datatype="html">
        <source> Close </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">16,17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="92c952c40beaef3b4dac2c411ee606731046d2c8" datatype="html">
        <source> View Details </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">19,20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ebd5b96fb776c97bd6986bacf5a857dbb2e4862c" datatype="html">
        <source> Approve Anyway </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/approve-batch/approve-batch.component.html</context>
          <context context-type="linenumber">22,23</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d095084d8121f7d563d02146a33b797536b1ceab" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{secondaryBtn}}"/> </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-dialog/confirm-dialog.component.html</context>
          <context context-type="linenumber">6,8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="177d8a296eda59a30ab4f1cc813685b2627b2629" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{primaryBtn | titlecase}}"/> </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/confirm-dialog/confirm-dialog.component.html</context>
          <context context-type="linenumber">9,11</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0dc2e6fb6d0244d5024e73ede409a45d5b0c8a64" datatype="html">
        <source>References</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/reference-url-dialog/reference-url-dialog.component.html</context>
          <context context-type="linenumber">1</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">501</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">168</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">176</context>
        </context-group>
      </trans-unit>
      <trans-unit id="236c7c866649d0fad4b5e6d286da8aa7d8666b6d" datatype="html">
        <source> UNDO </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/undo-snackbar/undo-snackbar.component.html</context>
          <context context-type="linenumber">7,8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7460287376550328285" datatype="html">
        <source>PROCESSING</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_dialogs/undo-snackbar/undo-snackbar.component.ts</context>
          <context context-type="linenumber">41</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7309063194246193228" datatype="html">
        <source>User logged out</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_guards/auth.guard.ts</context>
          <context context-type="linenumber">55</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2332375809*********" datatype="html">
        <source>Missing Subscription, Redirecting ...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/_guards/auth.guard.ts</context>
          <context context-type="linenumber">64</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2821179408673282599" datatype="html">
        <source>Home</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">17</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7619560701830330401" datatype="html">
        <source>Loading</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">23</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7430680760319628492" datatype="html">
        <source>Review</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">67</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6707361102856436710" datatype="html">
        <source>Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">35</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4930506384627295710" datatype="html">
        <source>Settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">41</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4768592761478998496" datatype="html">
        <source>You are offline</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">47</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7911416166208830577" datatype="html">
        <source>Help</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app-routing.module.ts</context>
          <context context-type="linenumber">53</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4fdd510f100358a2afa2dc81aa1a64fc6dd6f042" datatype="html">
        <source>Not Supported for Current resolution</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4063248990862762789" datatype="html">
        <source>dataX R2E: Item Data Enrichment - </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.ts</context>
          <context context-type="linenumber">88</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6691324072815722351" datatype="html">
        <source>dataX R3B: Item Data Audit &amp; Enhancement - </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/app.component.ts</context>
          <context context-type="linenumber">92</context>
        </context-group>
      </trans-unit>
      <trans-unit id="63eb45ca14797d50364b56efc165183ed6ead9c1" datatype="html">
        <source>View uploaded batches and monitor their progress</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">11</context>
        </context-group>
      </trans-unit>
      <trans-unit id="92eee6be6de0b11c924e3ab27db30257159c0a7c" datatype="html">
        <source>Home</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">34</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f4331c9463fd60aab6049ba34bc1352161fa2d8d" datatype="html">
        <source>Review content at SKU level.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">44</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6523895cfd110d53667e04c82c0f769c19d8ee74" datatype="html">
        <source>Review</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">67</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9541a91c89942b13cff2d65eb5d01796172be0d0" datatype="html">
        <source>Monitor the enrichment processes at SKU level.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
      </trans-unit>
      <trans-unit id="48dfb134cb8d324bb45f2f9f3bd7d7afc49bc3f8" datatype="html">
        <source>Products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">101</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fe1aae48977d43d27a42764a8c62acccd108bae4" datatype="html">
        <source>Set API usage.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">112</context>
        </context-group>
      </trans-unit>
      <trans-unit id="121cc5391cd2a5115bc2b3160379ee5b36cd7716" datatype="html">
        <source>Settings</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">134</context>
        </context-group>
      </trans-unit>
      <trans-unit id="85b79c9064aed1ead31ace985f31aa1363f6bdaf" datatype="html">
        <source>Help</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/side-nav/side-nav.component.html</context>
          <context context-type="linenumber">167</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d0227a069d21cc5c6951d829cbf135be5b5bc767" datatype="html">
        <source>Log Out</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/top-nav/top-nav.component.html</context>
          <context context-type="linenumber">99</context>
        </context-group>
      </trans-unit>
      <trans-unit id="244aae9346da82b0922506c2d2581373a15641cc" datatype="html">
        <source>Email</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fcaecbb9243a2ba43fd7d2f212c33ec8df5f64f1" datatype="html">
        <source><EMAIL></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">15</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c6ab23b42faa43ed9cb09f9168566c86611c9a44" datatype="html">
        <source>Your feedback</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7bedf780aa4921bb525d0fc5c1468f1206a208ba" datatype="html">
        <source>Type your text here…</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">495</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">570</context>
        </context-group>
      </trans-unit>
      <trans-unit id="17a9d3860d9ad593dd09a9f934e03999d9e76a7a" datatype="html">
        <source> Cancel </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">28,29</context>
        </context-group>
      </trans-unit>
      <trans-unit id="59804d9b69f39f46506a9bb69e8eed79c3eef8fd" datatype="html">
        <source> Save </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/help/help.component.html</context>
          <context context-type="linenumber">33,34</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d2baf6889fbbd6b216109a0a822b611354bb0d1e" datatype="html">
        <source>Search by Batch Id, Name or Description</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">6</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e2b2167fc47d31dee70bfb8208daf40a1220b274" datatype="html">
        <source> Recent</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">20</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">21</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">22</context>
        </context-group>
      </trans-unit>
      <trans-unit id="68ff1c120cd666fdcafb27e420a58bedd20b6480" datatype="html">
        <source>Custom Range  </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8d40a877c935d13b6d501f3094f5c849789227d1" datatype="html">
        <source>Upload batch of SKUs to be enriched.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">69</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a2d63bc696315b11e3010f46f0231360faf71d56" datatype="html">
        <source><x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon class=&quot;upload-btn-icon&quot;&gt;"/>add<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/> Upload New Batch </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">70,71</context>
        </context-group>
      </trans-unit>
      <trans-unit id="265ee68edfe57e510270da31ec99f67d94346009" datatype="html">
        <source> Reset </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">74,75</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">81,82</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">88,89</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">86,87</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e9206a9068381f7115736176f2bded6674a54db5" datatype="html">
        <source>View comments</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">76</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">83</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">90</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ac0b90a4dc259e5f1f3ce6b67da8bd8334d579f8" datatype="html">
        <source>Copy text</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">121</context>
        </context-group>
      </trans-unit>
      <trans-unit id="cf467767e8a4ad1145432712daa5728afbf39ae7" datatype="html">
        <source>Download input file</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">145</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d7b35c384aecd25a516200d6921836374613dfe7" datatype="html">
        <source>Cancel</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">233</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">581</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">487</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">578</context>
        </context-group>
      </trans-unit>
      <trans-unit id="826b25211922a1b46436589233cb6f1a163d89b7" datatype="html">
        <source>Delete</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">237</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">245</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">449</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">162</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">359</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">446</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e972e3f5ec910d9d1513545389dd0fcda77d0947" datatype="html">
        <source>Approve the batch</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">254</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6dc7fa16d9103d24f919e62bb41ca1598e244933" datatype="html">
        <source> Approve </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">256,257</context>
        </context-group>
      </trans-unit>
      <trans-unit id="bce22c8762862737d9878a3027ae397723aeb315" datatype="html">
        <source>Download enriched data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">289</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d190b337376ad098c70ad57691be1d404e813720" datatype="html">
        <source> Download <x id="TAG_IMG" ctype="image" equiv-text="&lt;img [src]=&quot;
                            t.menuOpen
                              ? &apos;../../assets/images/landing-page/up-arrow-white.svg&apos;
                              : &apos;../../assets/images/landing-page/down-arrow-white.svg&apos;
                          &quot; /&gt;"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">290,295</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5c554a2d805b39c3199f539aa34bd843a9ad3023" datatype="html">
        <source>Output File</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">312</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ee8f8008bae6ce3a49840c4e1d39b4af23d4c263" datatype="html">
        <source>Assets</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">316</context>
        </context-group>
      </trans-unit>
      <trans-unit id="377cfe4ba52c469fd7080707c122440411d86f20" datatype="html">
        <source>Insufficient Data SKUs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">334</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5b2b053aab96de9fe69ae0b94cecf02228d90427" datatype="html">
        <source>Copy reference</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">370</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0bf21e696bd2232ad574df23453e9e3129d9946d" datatype="html">
        <source>No batch in queue. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">405,406</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f3eedbdf565df433bb9f992d004ab49b3be8766" datatype="html">
        <source>No batch in progress. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">407,408</context>
        </context-group>
      </trans-unit>
      <trans-unit id="50a12600c3d7d7894123cc2cc44140916ca29df5" datatype="html">
        <source>Processing has not started. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">409,410</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b9c9e0d3caf491ec3be922fb679e7608b7a9a545" datatype="html">
        <source>No batch has been approved yet. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">411,412</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9691c2427ae565ef8cf6591b8e559339322ed93c" datatype="html">
        <source>No batch has been cancelled. </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">413,414</context>
        </context-group>
      </trans-unit>
      <trans-unit id="339ba1401f1dba2ab7bf8733f58b5cd49cef1eec" datatype="html">
        <source>Upload New Batch</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">436</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b6bed96af13bc125110fc3f5f51717df732221c1" datatype="html">
        <source>Drag and Drop your files here</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">446</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0e94a9e11289a09896d406059ec243204e328fad" datatype="html">
        <source>Batch Name*</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">466</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e0ebf17363e9df7dcb39da79e026a10141f8c965" datatype="html">
        <source>Eg. Batch_104.csv</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">468</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1034826c0b84eaa77de6438fa5ab414e0331857b" datatype="html">
        <source> Batch name is required </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">471,472</context>
        </context-group>
      </trans-unit>
      <trans-unit id="613f7ad7623a7e288929ce093480d6be05fab94d" datatype="html">
        <source> Batch name should not exceed 100 characters </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">474,475</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f86f859d52c766a9a3c48b648c8a291b726c78b" datatype="html">
        <source>Input Format*</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">478</context>
        </context-group>
      </trans-unit>
      <trans-unit id="564eefde47882585f94b906f3bf20c1ffecb85cb" datatype="html">
        <source>Output Format*</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">486</context>
        </context-group>
      </trans-unit>
      <trans-unit id="eec715de352a6b114713b30b640d319fa78207a0" datatype="html">
        <source>Description</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">493</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">567</context>
        </context-group>
      </trans-unit>
      <trans-unit id="cbec952a0ff4faf2f56f0e6014c391ae87aedb34" datatype="html">
        <source> Description should not exceed 2000 characters </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">498,499</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b9a4505bbf4041d75de176023fd16cee23633834" datatype="html">
        <source> Reference should not exceed 1000 characters </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">514,515</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6bb8c7f06a0a1b50aa1c9a09012fa66350d4cdef" datatype="html">
        <source> Maximum of 10 References can be added </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">531,532</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4cf935702f186c4cd692d2076a82feb47572f411" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{ uploadButtonLabel }}"/> </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">540,542</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d9a6d2f30e57a02a6b36a7773c39a3791c42ae9c" datatype="html">
        <source>Add Tag</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">549</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e1b8c658165692659d6ad0a7eb28f782cb011dda" datatype="html">
        <source>Create New Tag</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">563</context>
        </context-group>
      </trans-unit>
      <trans-unit id="dfb8f871e1dbe50025b54267a4466e2a480801fc" datatype="html">
        <source>Type your text here...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">565</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8855d0ad02000ba43dda7f8c164c1c8c0715bdb5" datatype="html">
        <source>Tag Color</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">580</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3cf1feec60b322423082c3d20d84823d17331da3" datatype="html">
        <source> Create Tag </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">595,596</context>
        </context-group>
      </trans-unit>
      <trans-unit id="db09b9d7f658ad13a3e90b2af2836711c97965e3" datatype="html">
        <source>Batch Log</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">602</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3537625483230066456" datatype="html">
        <source>Batch ID</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">62</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="4947493162669190104" datatype="html">
        <source>Name &amp; Description</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">64</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7886570921510760899" datatype="html">
        <source>Tags</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">67</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="4948663886508626048" datatype="html">
        <source>Created On</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">68</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3313063645488579833" datatype="html">
        <source>ETA</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">69</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="8837798131261467122" datatype="html">
        <source>Total Rows</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">70</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7317290694079257887" datatype="html">
        <source>Accepted</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">71</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">61</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="7040288612202036661" datatype="html">
        <source>Others</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">72</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3193976279273491157" datatype="html">
        <source>Actions</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">73</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">581</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="3807699453257291879" datatype="html">
        <source>Comments</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">74</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/details-routing.module.ts</context>
          <context context-type="linenumber">25</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="5937251202465808296" datatype="html">
        <source>More</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">75</context>
        </context-group>
        <note priority="1" from="description">header</note>
      </trans-unit>
      <trans-unit id="6932865105766151309" datatype="html">
        <source>Upload</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">112</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">604</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">653</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">674</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5303674099842475535" datatype="html">
        <source>Drag and Drop your files here</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">113</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">603</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">673</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5396470884744387564" datatype="html">
        <source>Download output file</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">121</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8899155811401566770" datatype="html">
        <source>Download insufficient data SKUs</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">122</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5015562350739866629" datatype="html">
        <source>File generation in progress. Please wait.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">123</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3966216282658417930" datatype="html">
        <source>In Queue</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">139</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="2914293374339727314" datatype="html">
        <source>Batches that are undergoing validation prior to processing</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">141</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="1780950485960518731" datatype="html">
        <source>In Progress</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">144</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">55</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="6932646515804747139" datatype="html">
        <source>Batches that are undergoing attribute enrichment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">146</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="4749295647449765550" datatype="html">
        <source>Processed</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">149</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="8630102797980407100" datatype="html">
        <source>Batches that have completed the enrichment process. Possible actions: Approve the batch / Download enriched data / Download input data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">151</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="5906272961955453157" datatype="html">
        <source>Approved</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">154</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="2160057240753842105" datatype="html">
        <source>Batches that have been approved post-enrichment. Possible actions: Download enriched data / Download input file</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">156</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="4043049091728011235" datatype="html">
        <source>Cancelled</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">159</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="3260816546187784699" datatype="html">
        <source>Batches that could not be processed because of invalid data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">161</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="5527182815413582066" datatype="html">
        <source>Modify ETA</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">167</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8111481560273086490" datatype="html">
        <source>Add ETA</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">168</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1676946925441352884" datatype="html">
        <source>Last Week</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">248</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">131</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">308</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">324</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5301451406229114785" datatype="html">
        <source>Last Month</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">253</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">136</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">313</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">329</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8065968369037299258" datatype="html">
        <source>Last Quarter</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">261</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">144</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">321</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">337</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5392341774767336507" datatype="html">
        <source>Copied!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">538</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7289632971788786960" datatype="html">
        <source>File can&apos;t be empty</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">554</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5388379548328685884" datatype="html">
        <source>Uploading..</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">570</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3484994613114698934" datatype="html">
        <source>Upload Complete</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">605</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">675</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7934833136974560675" datatype="html">
        <source>Retry</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">650</context>
        </context-group>
      </trans-unit>
      <trans-unit id="560328960231561562" datatype="html">
        <source>Error uploading file, Retry!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">654</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5889356921455381363" datatype="html">
        <source>Are you sure you want to approve  <x id="PH" equiv-text="batch_id"/>?
      No further processing is possible after the batch is approved.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">967,968</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5946223627892934995" datatype="html">
        <source>Are you sure you want to cancel <x id="PH" equiv-text="batch_id"/>?
      This will move the batch to &apos;Cancelled&apos; tab.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">970,971</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4193058338493003653" datatype="html">
        <source>Are you sure you want to delete <x id="PH" equiv-text="batch_id"/>?
      This will delete the batch from Cancelled.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">973,974</context>
        </context-group>
      </trans-unit>
      <trans-unit id="381510821082421688" datatype="html">
        <source>Are you sure you want to delete <x id="PH" equiv-text="batch_id"/>?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">975</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4082960634819166485" datatype="html">
        <source>APPROVE</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">981</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6943486270557756671" datatype="html">
        <source>CANCEL</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">983</context>
        </context-group>
      </trans-unit>
      <trans-unit id="841311519047609428" datatype="html">
        <source>DELETE</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">984</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7819314041543176992" datatype="html">
        <source>Close</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">985</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1018</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">858</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">839</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1080</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7019108526554724390" datatype="html">
        <source>Processing... please wait</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">1265</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/home/<USER>/context>
          <context context-type="linenumber">1279</context>
        </context-group>
      </trans-unit>
      <trans-unit id="771f5b8ed5db67f8a835828a038be46b4b57850e" datatype="html">
        <source>Access Denied!!</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/loading/loading.component.html</context>
          <context context-type="linenumber">9</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7a7641450828f8c58f57dc5a366ecb606ac1e130" datatype="html">
        <source> The page you are trying to reach cannot be accessed at the moment.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/loading/loading.component.html</context>
          <context context-type="linenumber">10</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1941c2ecd2dbae945e0a0847ff43be0a5806d9c3" datatype="html">
        <source>Search SKU, Product Name, Batch ID</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">8</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8946b0053f321c7db24853d1b213b1ea9794c789" datatype="html">
        <source>Custom Range</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">39</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4e5a20fd0c65bdc6830b2205b38fca3a6362d518" datatype="html">
        <source>Clear search and filters</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">79</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">87</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">85</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d460ef21f0de65e35a799269e80fd58a85c199e7" datatype="html">
        <source> Actions </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">127,128</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5e8a4dceb0a2c50245884c3298ab95ad051f6c53" datatype="html">
        <source>Move To</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">151</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">226</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">82</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">220</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b5b67ff97e9bcbac7c5c30a145ffc05845d105fa" datatype="html">
        <source>Nothing to display</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.html</context>
          <context context-type="linenumber">182</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">601</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">604</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1655609962937702330" datatype="html">
        <source>All SKUs that are undergoing enrichment</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">58</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="2355241115004164604" datatype="html">
        <source>All SKUs enriched by dataX with high confidence and don&apos;t need to be reviewed. You can still choose to make minor edits and add comments here. Possible actions: Leave as &apos;Accepted&apos; / Move to &apos;Review&apos; / Move to &apos;Rework&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">64</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="3955632182222112807" datatype="html">
        <source>SKUs that have undergone enrichment, but dataX has less confidence in, and requires your review. You can make minor edits and add comments here. Possible actions: Move to &apos;Accepted&apos; / Move to &apos;Rework&apos;</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">70</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="6583773138777204909" datatype="html">
        <source>Rework</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">73</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="8656057706886146282" datatype="html">
        <source>SKUs that need to be enriched again. Post-enrichment, the SKUs appear in the &apos;Accepted&apos; bucket</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">76</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="908856157228300595" datatype="html">
        <source>Insufficient Data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">79</context>
        </context-group>
        <note priority="1" from="description">bucket</note>
      </trans-unit>
      <trans-unit id="3790712685535029653" datatype="html">
        <source>SKUs that cannot be enriched because of missing data</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/products/products.component.ts</context>
          <context context-type="linenumber">82</context>
        </context-group>
        <note priority="1" from="description">bucket description</note>
      </trans-unit>
      <trans-unit id="d900c9c42e6f296a49a46079b5075765bfd04442" datatype="html">
        <source>Search MPN, Batch ID, Row ID</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="680d5c75b7fd8d37961083608b9fcdc4167b4c43" datatype="html">
        <source>Previous</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">77</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">26</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">78</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f732c304c7433e5a83ffcd862c3dce709a0f4982" datatype="html">
        <source>Next</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">81</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">29</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">81</context>
        </context-group>
      </trans-unit>
      <trans-unit id="a8026c4fe7ec0999f852e71393dca77c9e3b6f77" datatype="html">
        <source>Product Details</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">115</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">124</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8e45f9d35b6ab25da0cf068dd0f415ccef9fec9b" datatype="html">
        <source>Suggestions &amp; Edits</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">116</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">125</context>
        </context-group>
      </trans-unit>
      <trans-unit id="c5bb9f8936cb33db2ca351a1b87a984fdf11917c" datatype="html">
        <source>Comments</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">117</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">32</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">88</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">126</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1c1c277ec3b4c7b91026d05bd5d272c6a632b3ec" datatype="html">
        <source> View More</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
      </trans-unit>
      <trans-unit id="97e7da30de7499fcf212e1e0fd717865ce2aca3d" datatype="html">
        <source>View Details</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">223</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">216</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7ff1317eee6f83d2d803aff3cfecd41fb62461a7" datatype="html">
        <source> No Suggestions available </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">241</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">235</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5e15457d8c707fe6483a83ec1eec77f6fac95754" datatype="html">
        <source>Existing</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">266</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">260</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1fddd2204c0c24c6780510066ad45e28391581dc" datatype="html">
        <source>Suggestion</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">273</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">267</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e19737ed5663744451a005ad1dee2c8e218a4e26" datatype="html">
        <source> <x id="INTERPOLATION" equiv-text="{{ item.predictions.length - defSuggestionDisplayCount }}"/> More Suggestions <x id="START_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;mat-icon&gt;"/>arrow_drop_down<x id="CLOSE_TAG_MAT_ICON" ctype="x-mat_icon" equiv-text="&lt;/mat-icon&gt;"/></source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">321,324</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">315,318</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d235ae9c2af04d4b113c0fad596ab23c2fe884f9" datatype="html">
        <source>Bold</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">344</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">495</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">176</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">260</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">405</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">336</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">492</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5d03f644fa70efc613805db7af74801cebf33bf6" datatype="html">
        <source>Italic</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">345</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">497</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">177</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">279</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">261</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">407</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">337</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">494</context>
        </context-group>
      </trans-unit>
      <trans-unit id="46bceaa64c9270b80c5b57d8a4fc96951c059539" datatype="html">
        <source>Underline</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">347</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">499</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">179</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">263</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">409</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">339</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">496</context>
        </context-group>
      </trans-unit>
      <trans-unit id="b24ca8f3b3596794629fb1a49de783d5b45a275e" datatype="html">
        <source>Strikethrough</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">349</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">501</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">181</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">282</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">265</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">411</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">341</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">498</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3ec88737802269bcef0c5cfcec11e9fe3a04e60d" datatype="html">
        <source>Quote</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">351</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">503</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">183</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">284</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">267</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">413</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">343</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">500</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d421ed45622751903f64590eafaa621e2ab6fa72" datatype="html">
        <source>Insert code</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">353</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">505</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">185</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">286</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">269</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">415</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">345</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">502</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0a5a03015269d57edcd993e5a076775f00575cad" datatype="html">
        <source>Text colour</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">355</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">507</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">187</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">288</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">271</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">417</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">347</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">504</context>
        </context-group>
      </trans-unit>
      <trans-unit id="080a94f7efdea6749f7646f0979a70a589d431b1" datatype="html">
        <source>Heading 1</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">356</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">508</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">188</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">289</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">272</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">418</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">348</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">505</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0d18c103cf3a2219a37129ffdba7df3cf601fde7" datatype="html">
        <source>Heading 2</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">358</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">510</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">190</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">291</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">274</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">420</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">350</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">507</context>
        </context-group>
      </trans-unit>
      <trans-unit id="0b970d3dfb4512896620b6666777051a71e6e99e" datatype="html">
        <source>Numbered list</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">360</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">512</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">192</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">293</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">276</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">422</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">352</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">509</context>
        </context-group>
      </trans-unit>
      <trans-unit id="baa7b06bc3991ed764f12ff0f9425d2b38f3b7b7" datatype="html">
        <source>Bullet list</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">362</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">514</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">194</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">295</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">278</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">424</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">354</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">511</context>
        </context-group>
      </trans-unit>
      <trans-unit id="9fd8aadc05058a71bad23cd93c42195de960cc3e" datatype="html">
        <source>Insert link</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">364</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">516</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">196</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">297</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">280</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">426</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">356</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">513</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2f8ab7d965da02f8840e06c8ee2a907bda4d3832" datatype="html">
        <source>Insert image</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">370</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">523</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">204</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">303</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">288</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">434</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">362</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">520</context>
        </context-group>
      </trans-unit>
      <trans-unit id="589ef1ab38f4bc265af1aac3be9dfe6d4de8920e" datatype="html">
        <source>Attach a file</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">377</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">532</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">212</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">309</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">295</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">442</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">369</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">529</context>
        </context-group>
      </trans-unit>
      <trans-unit id="e9f040d6b896bf89223b6d6fdb01ba947bd9b8ee" datatype="html">
        <source>Go fullscreen</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">387</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">544</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">223</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">321</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">305</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">453</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">382</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">541</context>
        </context-group>
      </trans-unit>
      <trans-unit id="27dbb209d5d93ba67815d781ba7d8464f8aec6e2" datatype="html">
        <source>Close fullscreen</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">390</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">547</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">226</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">323</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">456</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">388</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">544</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8fce27c05e8133a86863a4a5e9135d55898bb114" datatype="html">
        <source> Send </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">416,417</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">326,327</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">414,415</context>
        </context-group>
      </trans-unit>
      <trans-unit id="28f86ffd419b869711aa13f5e5ff54be6d70731c" datatype="html">
        <source>Edit</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">446</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">356</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">443</context>
        </context-group>
      </trans-unit>
      <trans-unit id="047f50bc5b5d17b5bec0196355953e1a5c590ddb" datatype="html">
        <source>Update</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.html</context>
          <context context-type="linenumber">585</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">492</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">582</context>
        </context-group>
      </trans-unit>
      <trans-unit id="1453051811345638430" datatype="html">
        <source>Comment or mention others with @</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">192</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">159</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">154</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">211</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7568168175661014733" datatype="html">
        <source>View More</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">699</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">765</context>
        </context-group>
      </trans-unit>
      <trans-unit id="642420481690975524" datatype="html">
        <source>View less</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">703</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">769</context>
        </context-group>
      </trans-unit>
      <trans-unit id="4813239392166088334" datatype="html">
        <source>Are you sure you want to delete this comment?</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1015</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">859</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">836</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1077</context>
        </context-group>
      </trans-unit>
      <trans-unit id="7022070615528435141" datatype="html">
        <source>Delete</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1017</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.ts</context>
          <context context-type="linenumber">857</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">838</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1079</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6546893268910523216" datatype="html">
        <source>No more Products Available</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/review/review.component.ts</context>
          <context context-type="linenumber">1486</context>
        </context-group>
      </trans-unit>
      <trans-unit id="69fe62bc77a09c1e59c33c73535fa737cb87cb11" datatype="html">
        <source>API Usage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.html</context>
          <context context-type="linenumber">37</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5ca707824ab93066c7d9b44e1b8bf216725c2c22" datatype="html">
        <source>Filter</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.html</context>
          <context context-type="linenumber">40</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6762743264882388498" datatype="html">
        <source>Monthly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.ts</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="928838138686087140" datatype="html">
        <source>Weekly</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/settings/settings.component.ts</context>
          <context context-type="linenumber">21</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6508d8f187102b5b52f2c4ff41d2591e3a7a0b4c" datatype="html">
        <source>Batch Wise</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">12</context>
        </context-group>
      </trans-unit>
      <trans-unit id="227595d92b0a4e302b5bbdf167f2e22bfbd194ce" datatype="html">
        <source>SKU Wise</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ca0b86f1c8677e8091ffaab186f69c184382068e" datatype="html">
        <source>Search by Batch or SKU Id...</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">20</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3d472aefa1b3cf206c479990895e30e31b0f8117" datatype="html">
        <source>Mark as Resolved</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">101,102</context>
        </context-group>
      </trans-unit>
      <trans-unit id="490d7184d49f94f736f76f1b2cbbaeb9012bad9e" datatype="html">
        <source>No Comments yet.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">109</context>
        </context-group>
      </trans-unit>
      <trans-unit id="feca47f1da7c9cf5c241dacfcc4709c3fc50ef84" datatype="html">
        <source>To create a new comment, use @username</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">110</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d7169ace1e543bb2776a08b98e57e20de49b814c" datatype="html">
        <source> Cancel </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">253,254</context>
        </context-group>
      </trans-unit>
      <trans-unit id="d32a734b23805a90ed59be205fb10dcf11b347c1" datatype="html">
        <source> Update </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">258,259</context>
        </context-group>
      </trans-unit>
      <trans-unit id="2d8204977190ca57f3a68f08f547330e55880c47" datatype="html">
        <source> Send </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">349,350</context>
        </context-group>
      </trans-unit>
      <trans-unit id="986713bd00465ca6c60f68c344b6c8664e32cbe2" datatype="html">
        <source>No comments to display.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/comments/comments.component.html</context>
          <context context-type="linenumber">373</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3342433449995226607" datatype="html">
        <source>Product Details</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/details-routing.module.ts</context>
          <context context-type="linenumber">13</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6865523355931357851" datatype="html">
        <source>Review Mode</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/details-routing.module.ts</context>
          <context context-type="linenumber">19</context>
        </context-group>
      </trans-unit>
      <trans-unit id="3618ef2afb7155cd662abdd58c4929919f907b25" datatype="html">
        <source> Update </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">24,25</context>
        </context-group>
      </trans-unit>
      <trans-unit id="fba0699b3bac7753b497fa55765ace9d7b893883" datatype="html">
        <source> Product Details </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">40,41</context>
        </context-group>
      </trans-unit>
      <trans-unit id="51f9926620b9cc6b5476300c67e086754183812d" datatype="html">
        <source>Row ID :</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">63</context>
        </context-group>
      </trans-unit>
      <trans-unit id="ca0cb1f4418e3a70200a80a5a829a08a3a28760a" datatype="html">
        <source>Batch ID :</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">65</context>
        </context-group>
      </trans-unit>
      <trans-unit id="083338c1e13796fd0259d89493ec7b75be0d14d0" datatype="html">
        <source>Read only</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">121</context>
        </context-group>
      </trans-unit>
      <trans-unit id="01120284526728d672843d2149895c9b2364c71b" datatype="html">
        <source> No Data found </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.html</context>
          <context context-type="linenumber">157,158</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5321159160191726759" datatype="html">
        <source>Please use Next Button to view more products</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">427</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1332</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5530715537995053237" datatype="html">
        <source>You have viewed all the products in this batch</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">461</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1374</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8998179362936748717" datatype="html">
        <source>OK</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/product-details/product-details.component.ts</context>
          <context context-type="linenumber">462</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.ts</context>
          <context context-type="linenumber">1375</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f6714615c2540e04519058d911785ab23d66b44b" datatype="html">
        <source> Product Details </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">101,102</context>
        </context-group>
      </trans-unit>
      <trans-unit id="f909d1da7fe5caf97324309df6b6af43f72809df" datatype="html">
        <source> View More</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/details/review-mode/review-mode.component.html</context>
          <context context-type="linenumber">191</context>
        </context-group>
      </trans-unit>
      <trans-unit id="707147927148181991" datatype="html">
        <source>Items per page: </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">86</context>
        </context-group>
      </trans-unit>
      <trans-unit id="797234997496878479" datatype="html">
        <source>Next page </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">87</context>
        </context-group>
      </trans-unit>
      <trans-unit id="8642445555713453888" datatype="html">
        <source>First page</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">88</context>
        </context-group>
      </trans-unit>
      <trans-unit id="6000244294113297981" datatype="html">
        <source>Last page </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">89</context>
        </context-group>
      </trans-unit>
      <trans-unit id="5703462352676845579" datatype="html">
        <source>Previous page </source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/material.ts</context>
          <context context-type="linenumber">90</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
