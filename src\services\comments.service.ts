import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Globals } from '../app/_globals/endpoints.global';
import { Observable, of, pipe, throwError } from 'rxjs';
import { map, catchError, delay } from 'rxjs/operators';
import { ENDPOINTS } from '../app/_globals/endpoints';

@Injectable({
  providedIn: 'root',
})
export class CommentsService {
  private endpoints: any = ENDPOINTS;
  private httpOptions: HttpHeaders;
  constructor(private globals: Globals, private http: HttpClient) {
    this.httpOptions = new HttpHeaders({
      'Content-Type': 'application/json',
    });
  }

  /**
   * get comments list
   * @param subs_id
   * @param category
   * @param id
   * @returns
   */
  getCommentsList = (subs_id: string, page: string, size: string, category: string, id: string, q: string, comment_thread: string) => {
    const commentsListEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'commentsList',
      subs_id
    );
    const options = {
      params: new HttpParams()
        .set('page', page)
        .set('page_size', size)
        .set('category', category)
        .set('q', q)
        .set('comment_thread', comment_thread),
    };
    if (id) {
      options.params = options.params.append('category_id', id);
    }
    return this.http.get(commentsListEndpoint, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * post comment
   * @param subs_id
   * @param obj
   * @returns
   */
  postComment = (subs_id: string, type: string, id: string, obj: any) => {
    const options =
      type == 'batch'
        ? {
            params: new HttpParams().set('batch_id', id),
          }
        : {
            params: new HttpParams().set('row_id', id),
          };
    const postCommentEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'postComment',
      subs_id
    );
    return this.http.post(postCommentEndpoint, obj, options).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * get user names to tag
   * @param subs_id
   * @param q
   * @returns
   */
  getUserNamesToTag = (subs_id: string) => {
    const commentsListEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'userNamesToTag',
      subs_id
    );
    // const options = {
    //   params: new HttpParams().set('q', q),
    // };
    return this.http.get(commentsListEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * Resolve comment
   * @param subs_id
   * @param type
   * @param id
   * @returns
   */
  resolveComment = (subs_id: string, type: string, id: string) => {
    const options = {
      params: new HttpParams().set('category_id', id).set('category', type),
    };
    const postCommentEndpoint = this.globals.urlJoinWithParam(
      'comments',
      'resolve',
      subs_id
    );
    return this.http
      .patch(postCommentEndpoint, { resolve: true }, options)
      .pipe(
        map((response: any) => {
          return response;
        }),
        catchError((error) => throwError(error))
      );
  };

  /**
   * edit comment
   * @param subs_id
   * @param comment_id
   * @param obj
   */
  updateComment = (subs_id: string, comment_id: string, obj: any) => {
    const editCommentEndpoint = this.globals.urlJoinWithTwoParam(
      'comments',
      'edit',
      subs_id,
      comment_id
    );
    return this.http.patch(editCommentEndpoint, obj).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  /**
   * delete comment
   * @param subs_id
   * @param comment_id
   * @param obj
   * @returns
   */
  deleteComment = (subs_id: string, comment_id: string) => {
    const deleteCommentEndpoint = this.globals.urlJoinWithTwoParam(
      'comments',
      'delete',
      subs_id,
      comment_id
    );
    return this.http.delete(deleteCommentEndpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };

  uploadImage(file:File, mode:string, categoryID:string,) {
    const fd = new FormData();

    if(file) {
      fd.append('file', file, file.name);
    }

    const endpoint = this.globals.urlJoinWithParam(
      'comments',
      'upload',
      categoryID
    );

    return this.http.post(endpoint, fd);
  }

  getEmbeddedImage(subs_id: string, attach_id: string) {
    const endpoint = this.globals.urlJoinWithTwoParam(
      'comments',
      'image_url',
      subs_id,
      attach_id
    );

    return this.http.get(endpoint).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error) => throwError(error))
    );
  };
}
