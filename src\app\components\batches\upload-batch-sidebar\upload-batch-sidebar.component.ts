import { Component, EventEmitter, Output, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ChipsModule } from 'cax-design-system/chips';
import { DropdownModule } from 'cax-design-system/dropdown';
import { InputTextModule } from 'cax-design-system/inputtext';
import { InputTextareaModule } from 'cax-design-system/inputtextarea';
import { UploadModule } from 'cax-design-system/upload';
import { ButtonModule } from 'cax-design-system/button';
import { CommonModule } from '@angular/common';
import { HomeService } from '../../../../services/home.service';

interface TemplateOption {
    label: string;
    value: string;
}

@Component({
    selector: 'app-upload-batch-sidebar',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        DropdownModule,
        InputTextareaModule,
        InputTextModule,
        ChipsModule,
        UploadModule,
        ButtonModule
    ],
    templateUrl: './upload-batch-sidebar.component.html',
    styleUrl: './upload-batch-sidebar.component.scss',
})
export class UploadBatchSidebarComponent implements OnInit {
    @Input() visible: boolean = false;
    @Input() subscriptionId: string = '';
    @Output() visibleChange = new EventEmitter<boolean>();
    @Output() uploadComplete = new EventEmitter<any>();

    uploadForm: FormGroup;
    templateOptions: TemplateOption[] = [];
    selectedFiles: File[] = [];
    uploadProgress: number = 0;
    isUploading: boolean = false;
    tags: string[] = [];

    constructor(
        private fb: FormBuilder,
        private homeService: HomeService
    ) {
        this.uploadForm = this.fb.group({
            batchName: ['', [Validators.required, Validators.minLength(1)]],
            description: [''],
            templateId: ['', Validators.required],
            tags: [[]],
            file: [null, Validators.required]
        });
    }

    ngOnInit() {
        this.loadTemplates();
    }

    private loadTemplates() {
        if (!this.subscriptionId) return;
        
        this.homeService.getTemplateId(this.subscriptionId, 'all').subscribe({
            next: (response) => {
                this.templateOptions = response.result?.map((template: any) => ({
                    label: template.name || template.display_name,
                    value: template.id || template.template_id
                })) || [];
            },
            error: (error) => {
                console.error('Error loading templates:', error);
                this.templateOptions = [
                    { label: 'Default Template', value: 'default' }
                ];
            }
        });
    }

    onFileSelect(event: any) {
        const files = event.files || event.target.files;
        if (files && files.length > 0) {
            this.selectedFiles = Array.from(files);
            this.uploadForm.patchValue({ file: this.selectedFiles[0] });
        }
    }

    onFileRemove(event: any) {
        this.selectedFiles = [];
        this.uploadForm.patchValue({ file: null });
    }

    onTagAdd(event: any) {
        const value = event.value;
        if (value && !this.tags.includes(value)) {
            this.tags.push(value);
            this.uploadForm.patchValue({ tags: this.tags });
        }
    }

    onTagRemove(event: any) {
        const value = event.value;
        this.tags = this.tags.filter(tag => tag !== value);
        this.uploadForm.patchValue({ tags: this.tags });
    }

    onSubmit() {
        if (this.uploadForm.valid && !this.isUploading) {
            this.isUploading = true;
            this.uploadProgress = 0;

            const formData = new FormData();
            const formValue = this.uploadForm.value;

            formData.append('name', formValue.batchName);
            formData.append('description', formValue.description || '');
            formData.append('template_id', formValue.templateId);
            formData.append('file', formValue.file);
            
            if (formValue.tags && formValue.tags.length > 0) {
                formValue.tags.forEach((tag: string) => {
                    formData.append('tags[]', tag);
                });
            }

            // Simulate upload progress
            const progressInterval = setInterval(() => {
                this.uploadProgress += 10;
                if (this.uploadProgress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 200);

            // Simulate actual upload - replace with real API call
            setTimeout(() => {
                clearInterval(progressInterval);
                this.uploadProgress = 100;
                
                setTimeout(() => {
                    this.isUploading = false;
                    this.uploadComplete.emit({
                        success: true,
                        message: 'Batch uploaded successfully',
                        data: formValue
                    });
                    this.resetForm();
                    this.onCancel();
                }, 500);
            }, 2000);
        } else {
            this.markFormGroupTouched();
        }
    }

    onCancel() {
        this.resetForm();
        this.visible = false;
        this.visibleChange.emit(false);
    }

    private resetForm() {
        this.uploadForm.reset();
        this.selectedFiles = [];
        this.tags = [];
        this.uploadProgress = 0;
        this.isUploading = false;
    }

    private markFormGroupTouched() {
        Object.keys(this.uploadForm.controls).forEach(key => {
            const control = this.uploadForm.get(key);
            control?.markAsTouched();
        });
    }

    get isFormValid(): boolean {
        return this.uploadForm.valid && this.selectedFiles.length > 0;
    }

    getFieldError(fieldName: string): string {
        const field = this.uploadForm.get(fieldName);
        if (field?.errors && field.touched) {
            if (field.errors['required']) {
                return `${fieldName} is required`;
            }
            if (field.errors['minlength']) {
                return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
            }
        }
        return '';
    }
}


