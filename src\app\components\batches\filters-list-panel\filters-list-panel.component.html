<div class="filters-container">
    <div class="filter-count">
        <span>Filters ({{ object.keys(filtersApplied).length }})</span>
        <cax-button
            *ngIf="object.keys(filtersApplied).length"
            [label]="'Remove All'"
            [severity]="'danger'"
            [link]="true"
            (click)="removeAllFilters()"
            [size]="'small'"></cax-button>
    </div>
    <cax-divider [style]="{ margin: '4px 0' }"></cax-divider>
    <div *ngIf="object.keys(filtersApplied).length" class="filter-list">
        <li *ngFor="let entry of getEntries(filtersApplied)">
            <div>
                <span class="filter-column">{{ getColumnName(entry[0]) }}</span>
                <span class="filter-type">{{ getFilterString(entry[1]) }}</span>
            </div>
            <cax-button
                (click)="clearFilter()"
                [icon]="'cax cax-close'"
                [size]="'small'"
                severity="secondary"
                outlined="true"></cax-button>
        </li>
    </div>
    <div *ngIf="!object.keys(filtersApplied).length">
        <span>No filters applied</span>
    </div>
</div>
